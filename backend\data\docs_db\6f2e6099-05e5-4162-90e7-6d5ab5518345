## 3. Model Design and Training

Multi-layer perceptrons ( MLP s) for pixel-level classification (<PERSON><PERSON> and <PERSON>, 1989; <PERSON> and <PERSON>, 1989) appeared soon after the publication of the seminal backpropagation paper (<PERSON><PERSON><PERSON><PERSON> et al., 1986), but these shallow feed-forward networks had many drawbacks (<PERSON><PERSON><PERSON> et al., 1998), including an excessive number of parameters, lack of invariance, and disregard for the inherent structure present in images.

CNN s are deep feedforward neural networks designed to extract progressively more abstract features from multidimensional signals (1-D signals, 2-D images, 3-D video, etc.) (<PERSON><PERSON><PERSON> et al., 2015). Therefore, in addition to addressing the aforementioned problems of MLP s, CNN s automate feature engineering (<PERSON><PERSON> et al., 2013), that is, the design of algorithms that can transform raw signal values to discriminative features. Another advantage of CNNs over traditional machine learning classifiers is that they require minimal preprocessing of the input data. Due to their significant advantages, CNN s have become the method of choice in many medical image analysis applications over the past decade (<PERSON><PERSON><PERSON><PERSON> et al., 2017). The key enablers in this deep learning revolution were: (i) the availability of massive data sets; (ii) the availability of powerful and inexpensive graphics processing units; (iii) the development of better network architectures, learning algorithms, and regularization techniques; and (iv) the development of open-source deep learning frameworks.

Semantic segmentation may be understood as the attempt to answer the parallel and complementary questions 'what' and 'where' in a given image. The former is better answered by translation-invariant global features, while the latter requires welllocalized features, posing a challenge to deep models. CNN s for pixel-level classification first appeared in the mid-2000s (Ning et al., 2005), but their use accelerated after the seminal paper on FCN s by Long et al. (2015), which, along with U-Net (<PERSON><PERSON> et al., 2015), have become the basis for many state-of-the-art segmentation models. In contrast to classification CNN s (e.g., LeNet, AlexNet, VGG , GoogLeNet, ResNet), FCN s easily cope with arbitrary-sized input images.


## 3.1. Architecture

An ideal skin lesion segmentation algorithm is accurate, computationally inexpensive, invariant to noise and input transformations, requires little training data and is easy to implement and train. Unfortunately, no algorithm has, so far, been able to achieve these conflicting goals. DL -based segmentation tends towards accuracy and invariance at the cost of computation and training data. Ease of implementation is debatable: on the one hand, the algorithms often forgo cumbersome preprocessing, postprocessing, and feature engineering steps. On the other hand, tuning and optimizing them is often a painstaking task.

As shown in Fig. 6, we have classified the existing literature into single-network models, multiple-network models, hybridfeature models, and Transformer models. The first and second groups are somewhat self-descriptive, but notice that the latter is further divided into ensembles of models, multi-task methods (often performing simultaneous classification and segmentation), and GAN s. Hybrid-feature models combine DL with hand-crafted features. Transformer models, as the name suggests, employ

<!-- page_break -->

Fig. 6: Taxonomy of DL -based skin lesion segmentation model architectures.

picture_counter_20 The image is a diagram illustrating various segmentation model architectures used in artificial intelligence techniques for diagnosing diseases. It is divided into three main categories: Single Network Models, Multiple Network Models, and Hybrid Feature Models. Each category has subcategories:

1. Single Network Models:
   - Shortcut Connections (§*******)
   - Conv. Modules (§*******)
   - Multi-scale Modules (§*******)
   - Attention Modules (§*******)
   - Recurrent CNNs (§*******)

2. Multiple Network Models:
   - Ensembles (§*******)
   - Multi-task Models (§*******)
   - GANs (§*******)

3. Hybrid Feature Models:
   - Transformer Models (§3.1.4)

The central node is labeled "Segmentation Model Architectures §3.1" and branches out to the different categories and subcategories.

Transformers either with or without CNNs for segmentation, and have started being used for skin lesion segmentation only recently. We classified works according to their most relevant feature, but the architectural improvements discussed in Section 3.1.1 also appear in the models listed in the other sections. In Fig. 7, we show how frequently di GLYPH&lt;11&gt; erent architectural modules appear in the 177 surveyed works, grouped by our taxonomy of model architectures (Fig. 6).

Table 3 summarizes all the 177 surveyed works in this review, with the following attributes for each work: type of publication, datasets, architectural modules, loss functions, and augmentations used, reported Jaccard index, whether the paper performed cross-dataset evaluation ( CDE ) and postprocessing ( PP ), and whether the corresponding code was released publicly. For papers that reported segmentation results on more than 1 dataset, we list all of them and list the performance on only one dataset, formatting that particular dataset in bold. Since ISIC 2017 is the most popular dataset (Fig. 3), wherever reported, we note the performance (Jaccard index) on ISIC 2017. For papers that do not report the Jaccard index and instead report the Dice score, we compute the former based on the latter and report this computed score denoted by an asterisk. Cross-dataset evaluation ( CDE ) refers to when a paper trained model(s) on one dataset but evaluated on another..


## 3.1.1. Single Network Models

The approaches in this section employ a single DL model, usually an FCN , following an encoder-decoder structure, where the encoder extracts increasingly abstract features, and the decoder outputs the segmentation mask. In this section, we discuss these architectural choices for designing deep models for skin lesion segmentation.

Earlier DL -based skin lesion segmentation works adopted either FCN (Long et al., 2015) or U-Net (Ronneberger et al., 2015).

<!-- page_break -->

Fig. 7: The frequency of utilization of di GLYPH&lt;11&gt; erent architectural modules in the surveyed studies. Shortcut connections, particularly, skip connections (112 papers) and residual connections (70 papers) are the two most frequent components in DL -based skin lesion segmentation models. Attention mechanisms learn dependencies between elements in sequences, either spatially or channel-wise, and are therefore used by several encoder-decoder-style segmentation models (41 papers). Dilated convolutions help expand the receptive field of CNN -models without any additional parameters, which is why they are the most popular variant of convolution in the surveyed studies (35 papers). Finally, papers using Transformers (12 papers) started appearing from 2021 onwards and are on the rise.

picture_counter_21 The image is a bar plot from a medical research paper demonstrating the use of artificial intelligence techniques in diagnosing diseases. The bar plot categorizes various AI techniques and their frequency of usage. The categories include Shortcut Connections, Convolutional Modules, Multi-scale Modules, GAN, Attention Modules, RCNN, and Transformer. Each category is further broken down into specific techniques such as dense connection, Transformer, recurrent CNN, attention module, GAN, pyramid pooling, image pyramid, parallel multi-scale convolution, factorized convolution, separable convolution, global convolution, dilated convolution, skip connection, and residual connection, which are color-coded in the legend. The y-axis represents the frequency, with Shortcut Connections being the most frequent, followed by Attention Modules and others.

FCN originally comprised a backbone of VGG 16 (Simonyan and Zisserman, 2014) CNN layers in the encoder and a single deconvolution layer in the encoder. The original paper proposes three versions, two with skip connections ( FCN -8 and FCN -16), and one without them (FCN-32). U-Net (Ronneberger et al., 2015), originally proposed for segmenting electron microscopy images, was rapidly adopted in the medical image segmentation literature. As its name suggests, it is a U-shaped model, with an encoder stacking convolutional layers that double in size filterwise, intercalated by pooling layers, and a symmetric decoder with pooling layers replaced by up-convolutions. Skip connections between corresponding encoder-decoder blocks improve the flow of information between layers, preserving low-level features lost during pooling and producing detailed segmentation boundaries.

U-Net frequently appears in the skin lesion segmentation literature both in its original form (Codella et al., 2017; Pollastri et al., 2020; Ramani and Ranjani, 2019) and modified forms (Tang et al., 2019a; Alom et al., 2019; Hasan et al., 2020), discussed below. Some works introduce their own models (Yuan et al., 2017; Al-Masni et al., 2018).

*******. Shortcut Connections. Connections between early and late layers in FCN s have been widely explored to improve both the forward and backward (gradient) information flow in the models, facilitating the training. The three most popular types of connections are described below.

Residual connections : Creating non-linear blocks that add their unmodified inputs to their outputs (He et al., 2016) alleviates gradient degradation in very deep networks. It provides a direct path for the gradient to flow through to the early layers of the network, while still allowing for very deep models. The technique appears often in skin lesion segmentation, in the implementation of the encoder (Sarker et al., 2018; Baghersalimi et al., 2019; Yu et al., 2017a) or both encoder and decoder (He et al., 2017; Venkatesh et al., 2018; Li et al., 2018a; Tu et al., 2019; Zhang et al., 2019a; He et al., 2018; Xue et al., 2018). Residual connections have also appeared in recurrent units (Alom et al., 2019, 2020), dense blocks (Song et al., 2019), chained pooling (He et al., 2017;

<!-- page_break -->

Li et al., 2018a; He et al., 2018), and 1-D factorized convolutions (Singh et al., 2019).

Skip connections appear in encoder-decoder architectures, connecting high-resolution features from the encoder's contracting path to the semantic features on the decoder's expanding path (Ronneberger et al., 2015). These connections help preserve localization, especially near region boundaries, and combine multi-scale features, resulting in sharper boundaries in the predicted segmentation. Skip connections are very popular in skin lesion segmentation because they are e GLYPH&lt;11&gt; ective and easy to implement (Zhang et al., 2019a; Baghersalimi et al., 2019; Song et al., 2019; Wei et al., 2019; Venkatesh et al., 2018; Azad et al., 2019; He et al., 2017; Alom et al., 2019; Sarker et al., 2018; Zeng and Zheng, 2018; Li et al., 2018a; Tu et al., 2019; Yu et al., 2017a; Singh et al., 2019; He et al., 2018; Xue et al., 2018; Alom et al., 2020; Vesal et al., 2018b; Liu et al., 2019b).

Dense connections expand the convolutional layers by connecting each layer to all its subsequent layers, concatenating their features (Huang et al., 2017). Iterative reuse of features in dense connections maximizes information flow forward and backward. Similar to deep supervision (Section 3.2.5), the gradient is propagated backwards directly through all previous layers. Several works (Zeng and Zheng, 2018; Song et al., 2019; Li et al., 2021c; Tu et al., 2019; Vesal et al., 2018b) integrated dense blocks in both the encoder and the decoder. Baghersalimi et al. (2019), Hasan et al. (2020) and Wei et al. (2019) used multiple dense blocks iteratively in only the encoder, while Li et al. (2018a) proposed dense deconvolutional blocks to reuse features from the previous layers. Azad et al. (2019) encoded densely connected convolutions into the bottleneck of their encoder-decoder to obtain better features.

*******. Convolutional Modules. As mentioned earlier, convolution not only provides a structural advantage, respecting the local connectivity structure of images in the output futures, but also dramatically improves parameter sharing since the parameters of a relatively small convolutional kernel are shared by all patches of a large image. Convolution is a critical element of deep segmentation models. In this section, we discuss some new convolution variants, which have enhanced and diversified this operation, appearing in the skin lesion segmentation literature.

Dilated convolution : In contrast to requiring full-resolution outputs in dense prediction networks, pooling and striding operations have been adopted in deep convolutional neural networks ( DCNN s) to increase the receptive field and diminish the spatial resolution of feature maps. Dilated or atrous convolutions are designed specifically for the semantic segmentation task to exponentially expand the receptive fields while keeping the number of parameters constant (Yu and Koltun, 2016). Dilated convolutions are convolutional modules with upsampled filters containing zeros between consecutive filter values. Sarker et al. (2018) and Jiang et al. (2019) utilized dilated residual blocks in the encoder to control the image field-of-view explicitly and incorporated multi-scale contextual information into the segmentation network. SkinNet (Vesal et al., 2018b) used dilated convolutions at the lower level of the network to enlarge the field-of-view and capture non-local information. Liu et al. (2019b) introduced dilated convolutions to the U-Net architecture, significantly improving the segmentation performance. Furthermore, di GLYPH&lt;11&gt; erent versions of the DeepLab architecture (Chen et al., 2017a,b, 2018a), which replace standard convolutions with dilated ones, have been used in skin lesion segmentation (Goyal et al., 2019a,b; Cui et al., 2019; Chen et al., 2018b; Canalini et al., 2019).

Separable convolution : Separable convolution or depth-wise separable convolution (Chollet, 2017) is a spatial convolution operation that convolves each input channel with its corresponding kernel. This is followed by a 1 GLYPH&lt;2&gt; 1 standard convolution to capture the

<!-- page_break -->

channel-wise dependencies in the output of depth-wise convolution. Depth-wise convolutions are designed to reduce the number of parameters and the computation of standard convolutions while maintaining the accuracy. DSNet (Hasan et al., 2020) and separable-Unet (Tang et al., 2019a) utilized depth-wise separable convolutions in the model to have a lightweight network with a reduced number of parameters. Adopted from the DeepLab architecture, Goyal et al. (2019b), Cui et al. (2019) and, Canalini et al. (2019) incorporated depth-wise separable convolutions in conjunction with dilated convolution to improve the speed and accuracy of dense predictions.

Global convolution : State-of-the-art segmentation models remove densely connected and global pooling layers to preserve spatial information required for full-resolution output recovery. As a result, by keeping high-resolution feature maps, segmentation models become more suitable for localization and, in contrast, less suitable for per-pixel classification, which needs transformation invariant features. To increase the connectivity between feature maps and classifiers, large convolutional kernels should be adopted. However, such kernels have a large number of parameters, which renders them computationally expensive. To tackle this, global convolutional network ( GCN ) modules adopt a combination of symmetric parallel convolutions in the form of 1 GLYPH&lt;2&gt; k + k GLYPH&lt;2&gt; 1 and k GLYPH&lt;2&gt; 1 + 1 GLYPH&lt;2&gt; k to cover a k GLYPH&lt;2&gt; k area of feature maps (Peng et al., 2017b). SeGAN (Xue et al., 2018) employed GCN modules with large kernels in the generator's decoder to reconstruct segmentation masks and in the discriminator architecture to optimally capture a larger receptive field.

Factorized convolution : Factorized convolutions (Wang et al., 2017) are designed to reduce the number of convolution filter parameters as well as the computation time through kernel decomposition when a high-dimensional kernel is substituted with a sequence of lower-dimensional convolutions. Additionally, by adding non-linearity between the composited kernels, the network's capacity may improve. FCA -Net (Singh et al., 2019) and MobileGAN (Sarker et al., 2019) utilized residual 1-D factorized convolutions (a sequence of k GLYPH&lt;2&gt; 1 and 1 GLYPH&lt;2&gt; k convolutions with ReLU non-linearity) in their segmentation architecture.

*******. Multi-scale Modules. In FCN s, taking semantic context into account when assigning per-pixel labels leads to a more accurate prediction (Long et al., 2015). Exploiting multi-scale contextual information, e GLYPH&lt;11&gt; ectively combining them as well as encoding them in deep semantic segmentation have been widely explored.

Image Pyramid : RefineNet (He et al., 2017) and its extension (He et al., 2018), MSFCDN (Zeng and Zheng, 2018), FCA-Net (Singh et al., 2019), and Abraham and Khan (2019) fed a pyramid of multi-resolution skin lesion images as input to their deep segmentation network to extract multi-scale discriminative features. RefineNet (He et al., 2017, 2018), Factorized channel attention network (FCA-Net (Singh et al., 2019)) and Abraham and Khan (2019) applied convolutional blocks to di GLYPH&lt;11&gt; erent image resolutions in parallel to generate features which are then up-sampled in order to fuse multi-scale feature maps. Multi-scale fully convolutional DenseNets ( MSFCDN (Zeng and Zheng, 2018)) gradually integrated multi-scale features extracted from the image pyramid into the encoder's down-sampling path. Also, Jafari et al. (2016, 2017) extracted multi-scale patches from clinical images to predict semantic labels and refine lesion boundaries by deploying local and global information. While aggregating the feature maps computed at various image scales improves the segmentation performance, it also increases the computational cost of the network.

Parallel multi-scale convolutions : Alternatively, given a single image resolution, multiple convolutional filters with di GLYPH&lt;11&gt; erent kernel sizes (Zhang et al., 2019a; Wang et al., 2019a; Jahanifar et al., 2018) or multiple dilated convolutions with di GLYPH&lt;11&gt; erent dilation

<!-- page_break -->

rates (Goyal et al., 2019a,b; Cui et al., 2019; Chen et al., 2018b; Canalini et al., 2019) can be adopted in parallel paths to extract multi-scale contextual features from images. DSM (Zhang et al., 2019a) integrated multi-scale convolutional blocks into the skip connections of an encoder-decoder structure to handle di GLYPH&lt;11&gt; erent lesion sizes. Wang et al. (2019a) utilized multi-scale convolutional branches in the bottleneck of an encoder-decoder architecture, followed by attention modules to selectively aggregate the extracted multi-scale features.

Pyramid pooling : Another way of incorporating multi-scale information into deep segmentation models is to integrate a pyramid pooling ( PP ) module in the network architecture (Zhao et al., 2017). PP fuses a hierarchy of features extracted from di GLYPH&lt;11&gt; erent subregions by adopting parallel pooling kernels of various sizes, followed by up-sampling and concatenation to create the final feature maps. Sarker et al. (2018) and Jahanifar et al. (2018) utilized PP in the decoder to benefit from coarse-to-fine features extracted by di GLYPH&lt;11&gt; erent receptive fields from skin lesion images.

Dilated convolutions and skip connections are two other types of multi-scale information extraction techniques, which are explained in Sections ******* and *******, respectively.

*******. Attention Modules. An explicit way to exploit contextual dependencies in the pixel-wise labeling task is the self-attention mechanism (Hu et al., 2018; Fu et al., 2019). Two types of attention modules capture global dependencies in spatial and channel dimensions by integrating features among all positions and channels, respectively. Wang et al. (2019a) and Sarker et al. (2019) leveraged both spatial and channel attention modules to recalibrate the feature maps by examining the feature similarity between pairs of positions or channels and updating each feature value by a weighted sum of all other features. Singh et al. (2019) utilized a channel attention block in the proposed factorized channel attention ( FCA ) blocks, which was used to investigate the correlation of di GLYPH&lt;11&gt; erent channel maps for extraction of relevant patterns. Inspired by attention U-Net (Oktay et al., 2018), multiple works (Abraham and Khan, 2019; Song et al., 2019; Wei et al., 2019) integrated a spatial attention gate in an encoder-decoder architecture to combine coarse semantic feature maps and fine localization feature maps. Kaul et al. (2019) proposed FocusNet which utilizes squeeze-and-excitation blocks into a hybrid encoder-decoder architecture. Squeeze-and-excitation blocks model the channel-wise interdependencies to re-weight feature maps and improve their representation power. Experimental results demonstrate that attention modules help the network focus on the lesions and suppress irrelevant feature responses in the background.

*******. Recurrent Convolutional Neural Networks. Recurrent convolutional neural networks ( RCNN ) integrate recurrent connections into convolutional layers by evolving the recurrent input over time (Pinheiro and Collobert, 2014). Stacking recurrent convolutional layers ( RCL ) on top of the convolutional layer feature extractors ensures capturing spatial and contextual dependencies in images while limiting the network capacity by sharing the same set of parameters in RCL blocks. In the application of skin lesion segmentation, Attia et al. (2017) utilized recurrent layers in the decoder to capture spatial dependencies between deep-encoded features and recover segmentation maps at the original resolution. r N -Net (Alom et al., 2020), RU-Net, and R2U-Net (Alom et al., 2019) incorporated RCL blocks into the FCN architecture to accumulate features across time in a computationally e GLYPH&lt;14&gt; cient way and boosted the skin lesion boundary detection. Azad et al. (2019) deployed a non-linear combination of the encoder feature and decoder feature maps by adding a bi-convolutional LSTM BConvLSTM ( ) in skip connections. BConvLSTM consists of two independent convolutional LSTM modules ( ConvLSTMs ) which process the feature maps into two directions of backward and forward

<!-- page_break -->

paths and concatenate their outputs to obtain the final output. Modifications to the traditional pooling layers were also proposed, using a dense pooling strategy (Nasr-Esfahani et al., 2019).


## 3.1.2. Multiple Network Models

Motivations for models comprising more than one DL sub-model are diverse, ranging from alleviating training noise and exploiting a diversity of features learned by di GLYPH&lt;11&gt; erent models to exploring synergies between multi-task learners. After examining the literature (Fig. 6), we further classified the works in this section into standard ensembles and multi-task models. We also discuss generative adversarial models, which are intrinsically multi-network models, in a separate category.

*******. Standard Ensembles. Ensemble models are widely used in machine learning, motivated by the hope that the complementarity of di GLYPH&lt;11&gt; erent models may lead to more stable combined predictions (Sagi and Rokach, 2018). Ensemble performance is contingent on the quality and diversity of the component models, which can be combined at the feature level (early fusion) or the prediction level (late fusion). The former combines the features extracted by the components and learns a meta-model on them, while the latter pools or combines the models' predictions with or without a meta-model.

All methods discussed in this section employ late fusion, except for an approach loosely related to early fusion (Tang et al., 2019a), which explores various learning-rate decay schemes, and builds a single model by averaging the weights learned at di GLYPH&lt;11&gt; erent epochs to bypass poor local minima during training. Since the weights correspond to features learned by the convolution filters, this approach can be interpreted as feature fusion.

Most works employ a single DL architecture with multiple training routines, varying configurations more or less during training (Canalini et al., 2019). The changes between component models may involve network hyperparameters: number of filters per block and their size (Codella et al., 2017); optimization and regularization hyperparameters: learning rate, weight decay (Tan et al., 2019b); the training set: multiple splits of a training set (Yuan et al., 2017; Yuan and Lo, 2019), separate models per class (Bi et al., 2019b); preprocessing: di GLYPH&lt;11&gt; erent color spaces (Pollastri et al., 2020); di GLYPH&lt;11&gt; erent pretraining strategies to initialize feature extractors (Canalini et al., 2019); or di GLYPH&lt;11&gt; erent ways to initialize the network parameters (Cui et al., 2019). Test-time augmentation may also be seen as a form of inference-time ensembling (Chen et al., 2018b; Liu et al., 2019b; Jahanifar et al., 2018) that combines the outputs of multiple augmented images to generate a more reliable prediction.

Bi et al. (2019b) trained a separate DL model for each class, as well as a separate classification model. For inference, the classification model output is used to weight the outputs of the category-specific segmentation networks. In contrast, Soudani and Barhoumi (2019) trained a meta 'recommender' model to dynamically choose, for each input, a segmentation technique from the top five scorers in the ISIC 2017 challenge, although their proposition was validated on a very small test set (10% of ISIC 2017 test set).

Several works also ensemble di GLYPH&lt;11&gt; erent model architectures for skin lesion segmentation. Goyal et al. (2019b) investigate multiple fusion approaches to avoid severe errors from individual models, comparing the average-, maximum- and minimum-pooling of their outputs. A common assumption is that the component models of the ensemble are trained independently, but Bi et al. (2017b) cascaded the component models, i.e., used the output of one model as the input of the next (in association with the actual image

<!-- page_break -->

input). Thus, each model attempts to refine the segmentation obtained by the previous one. They consider not only the final model output, but all the outputs in the cascade, making the technique a legitimate ensemble.

*******. Multi-task Models. Multi-task models jointly address more than one goal, in the hope that synergies among the tasks will improve overall performance (Zhang and Yang, 2022). This can be particularly helpful in medical image analysis, wherein aggregating tasks may alleviate the issue of insu GLYPH&lt;14&gt; cient data or annotations. For skin lesions, a few multi-task models exploiting segmentation and classification have been proposed (Chen et al., 2018b; Li and Shen, 2018; Yang et al., 2018; Xie et al., 2020b; Jin et al., 2021).

The synergy between tasks may appear when their models share common relevant features. Li and Shen (2018) assume that all features are shareable between the tasks, and train a single fully convolutional residual network to assign class probabilities at the pixel level. They aggregate the class probability maps to estimate both lesion region and class by weighted averaging of probabilities for di GLYPH&lt;11&gt; erent classes inside the lesion area. Yang et al. (2018) learn an end-to-end model formed by a shared convolutional feature extractor followed by three task-specific branches (one to segment skin lesions, one to classify them as melanoma versus nonmelanoma, and one to classify them as seborrheic keratosis versus non-seborrheic keratosis.) Similarly, Chen et al. (2018b) add a common feature extractor and separate task heads, and introduce a learnable gate function that controls the flow of information between the tasks to model the latent relationship between two tasks.

Instead of using a single architecture for classification and segmentation, Xie et al. (2020b) and Jin et al. (2021) use three CNNs in sequence to perform a coarse segmentation, followed by classification and, finally, a fine segmentation. Instead of shared features, these works exploit sequential guidance, in which the output of each task improves the learning of the next. While Xie et al. (2020b) feed the output of each network to the next, assuming that the classification network is a diagnostic category and a class activation map (Zhou et al., 2016), Jin et al. (2021) introduce feature entanglement modules, which aggregate features learned by di GLYPH&lt;11&gt; erent networks.

All multi-task models discussed so far have results suggesting complementarity between classification and segmentation, but there is no clear advantage among these models. The segmentation of dermoscopic features (e.g., networks, globules, regression areas) combined with the other tasks is a promising avenue of research, which could bridge classification and segmentation, by fostering the extraction of features that 'see' the lesion as human specialists do.

We do not consider in the hybrid group, two-stage models in which segmentation is used as ancillary preprocessing to classification (Yu et al., 2017a; Codella et al., 2017; Gonzalez-Diaz, 2018; Al-Masni et al., 2020), since without mutual influence (sharing of losses or features) or feedback between the two tasks, there is no opportunity for synergy.

Vesal et al. (2018a) stressed the importance of object localization as an ancillary task for lesion delineation, in particular deploying FasterRCNN (Ren et al., 2015) to regress a bounding box to crop the lesions before training a SkinNet segmentation model. While this two-stage approach considerably improves the results, it is computationally expensive (a fast nonDL -based bounding box detection algorithm was proposed earlier by Celebi et al. (2009a)). Goyal et al. (2019a) employed ROI detection with a deep extreme cut to extract the extreme points of lesions (leftmost, rightmost, topmost, bottommost pixels) and feed them, in a new auxiliary channel, to a segmentation model.

<!-- page_break -->

*******. Generative Adversarial Models. We discussed GAN s for synthesizing new samples, their main use in skin lesion analysis, in Section 2.2. In this section, we are interested in GAN s not for generating additional training samples, but for directly providing enhanced segmentation models. Adversarial training encourages high-order consistency in predicted segmentation by implicitly looking into the joint distribution of class labels and ground-truth segmentation masks.

Peng et al. (2019), Tu et al. (2019), Lei et al. (2020), and Izadi et al. (2018) use a U-Net-like generator that takes a dermoscopic image as input, and outputs the corresponding segmentation, while the discriminator is a traditional CNN which attempts to discriminate pairs of image and generated segmentation from pairs of image and ground-truth. The generator has to learn to correctly segment the lesion in order to fool the discriminator. Jiang et al. (2019) use the same scheme, with a dual discriminator. Lei et al. (2020) also employ a second discriminator that takes as input only segmentations (unpaired from input images).

Since the discriminator may trivially learn to recognize the generated masks due to the presence of continuous probabilities, instead of the sharp discrete boundaries of the ground-truths, Wei et al. (2019) and Tu et al. (2019) address this by pre-multiplying both the generated and real segmentations with the (normalized) input images before feeding them to the discriminator.

We discuss adversarial loss functions further in Section 3.2.8.


## 3.1.3. Hybrid Feature Models

Although the major strength of CNN s is their ability to learn meaningful image features without human intervention, a few works tried to combine the best of both worlds, with strategies ranging from employing pre- or postprocessing to enforce prior knowledge to adding hand-crafted features Providing the model with prior knowledge about the expected shape of skin lesionswhich is missing from CNN s-may improve the performance. Mirikharaji and Hamarneh (2018) encode shape information into an additional regularization loss, which penalizes segmentation maps that deviate from a star-shaped prior (Section 3.2.6).

Conditional random fields ( CRF s) use pixel-level color information models to refine the segmentation masks output by the CNN . While both Tschandl et al. (2019) and Adegun and Viriri (2020b) consider a single CNN , Qiu et al. (2020) combine the outputs of multiple CNN s into a single mask, before feeding it together with the input image to the CRF s. ¨ nver and Ayan (2019) U use GrabCut (Rother et al., 2004) to obtain the segmentation mask given the dermoscopy image and a region proposal obtained by the YOLO (Redmon et al., 2016) network. These methods regularize the CNN segmentation, which is mainly based on textural patterns, with expected priors based on the color of the pixels.

Works that combine hand-crafted features with CNN s follow two distinct approaches. The first consists of pre-filtering the input images to increase the contrast between the lesion and the surrounding skin. Techniques explored include local binary patterns ( LBP s) (Ross-Howe and Tizhoosh, 2018; Jayapriya and Jacob, 2020), wavelets (Ross-Howe and Tizhoosh, 2018), Laplacian pyramids (Pour and Seker, 2020), and Laplacian filtering (Saba et al., 2019). The second approach consists of predicting an additional segmentation mask to combine with the one generated by the CNN . Zhang et al. (2019b), for example, use LBP s to consider the textural patterns of skin lesions and guide the networks towards more refined segmentations. Bozorgtabar et al. (2017b) also employ LBP s combined with pixel-level color information to divide the dermoscopic image into superpixels, which are then scored as part of the lesion or the background. The score mask is then combined with the CNN output mask to compute the final segmentation mask. Despite the limited number of works devoted to integrating deep features with hand-crafted ones, the

<!-- page_break -->

results so far indicate that this may be a promising research direction.


## 3.1.4. Transformer Models

Initially proposed for natural language processing (Vaswani et al., 2017), Transformers have proliferated in the last couple of years in other areas, including computer vision applications, especially with improvements made over the years for optimizing the computational cost of self-attention (Parmar et al., 2018; Hu et al., 2019; Ramachandran et al., 2019; Cordonnier et al., 2019; Zhao et al., 2020; Dosovitskiy et al., 2020), and have consequently also been adapted for semantic segmentation tasks (Ranftl et al., 2021; Strudel et al., 2021; Zheng et al., 2021). For medical image segmentation, TransUNet (Chen et al., 2021) was one of the first works to use Transformers along with CNNs in the encoder of a U-Net-like encoder-decoder architecture, and Gulzar and Khan (2022) showed that TransUNet outperforms several CNN-only models for skin lesion segmentation. To reduce the computational complexity involved with high-resolution medical images, Cao et al. (2021) proposed the Swin-Unet architecture that uses selfattention within shifted windows (Liu et al., 2021b). For a comprehensive review of the literature of Transformers in general medical image analysis, we refer the interested readers to the surveys by He et al. (2022) and Shamshad et al. (2022).

Zhang et al. (2021b) propose TransFuse which parallelly computes features from CNN and Transformer modules, with the former capturing low-level spatial information and the latter responsible for modeling global context, and these features are then combined using a self-attention-based fusion module. Evaluation on the ISIC 2017 dataset shows superior segmentation performance and faster convergence. The multi-compound Transformer (Ji et al., 2021) leverages Transformer-based self-attention and cross-attention modules between the encoder and the decoder components of U-Net to learn rich features from multi-scale CNN features. Wang et al. (2021a) incorporate boundary-wise prior knowledge in segmentation models using a boundary-aware Transformer (BAT) to deal with the ambiguous boundaries in skin lesion images. More recently, Wu et al. (2022a) introduce a feature-adaptive Transformer network (FAT-Net) that comprised of a dual CNN-Transformer encoder, a light-weight trainable feature-adaptation module, and a memory-e GLYPH&lt;14&gt; cient decoder using a squeeze-and-excitation module. The resulting segmentation model is more accurate at segmenting skin lesions while also being faster (fewer parameters and computation) than several CNN-only models.