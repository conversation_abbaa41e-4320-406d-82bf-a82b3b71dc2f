## 4.1. Segmentation Annotation

Obtaining ground-truth segmentations is paramount for the objective evaluation of segmentation algorithms. For synthetically generated images (Section 2.2), ground-truth segmentations may be known by construction, either by applying parallel transformations to the original ground-truth masks in the case of traditional data augmentation, or by training generative models to synthesize images paired with their segmentation masks.

For images obtained from real patients, however, human experts have to provide the ground-truth segmentations. Various workflows have been proposed to reconcile the conflicting goals of ease of learning, speed, accuracy, and flexibility of annotation.

<!-- page_break -->

On one end of the spectrum, the expert traces the lesion by hand, on images of the skin lesion printed on photographic paper, which are then scanned (<PERSON><PERSON> et al., 2015). The technique is easy to learn and fast, but the printing and scanning procedure limits the accuracy, and the physical nature of the annotations makes corrections burdensome. On the other end of the spectrum, the annotation is performed on the computer, by a semi-automated procedure (<PERSON><PERSON> et al., 2019), with an initial border generated by a segmentation algorithm, which is then refined by the expert using an annotation software, by adjusting the parameters of the segmentation algorithm manually. This method is fast and easy to correct, but there might be a learning curve, and its accuracy depends on which algorithm is employed and how much the experts understand it.

By far, the commonest annotation method in the literature is somewhere in the middle, with fully manual annotations performed on a computer. The skin lesion image file may be opened either in a raster graphics editor (e.g., GNU Image Manipulation Program ( GIMP ) or Adobe Photoshop), or in a dedicated annotation software (Ferreira et al., 2012), where the expert traces the borders of the lesion using a mouse or stylus, with continuous freehand drawing, or with discrete control points connecting line segments (resulting in a polygon (Codella et al., 2019)) or smooth curve segments (e.g., cubic B-splines (Celebi et al., 2007a)). This method provides a good compromise, being easy to implement, fast, and accurate to perform, after an acceptable learning period for the annotator.


## 4.2. Inter-Annotator Agreement

Formally, dataset ground-truths can be viewed as samples of an estimator of the true label, which can never be directly observed (Smyth et al., 1995). This problem is often immaterial for classification, when annotation noise is small. However, in medical image segmentation, ground-truths su GLYPH&lt;11&gt; er from both biases (systematic deviations from the 'ideal') and significant noise (Zijdenbos et al., 1994; Chalana and Kim, 1997; Guillod et al., 2002; Grau et al., 2004; Bogo et al., 2015; Lampert et al., 2016), the latter appearing as inter-annotator (di GLYPH&lt;11&gt; erent experts) and intra-annotator (same expert at di GLYPH&lt;11&gt; erent times) variability.

In the largest study of its kind to date, Fortina et al. (2012) measured the inter-annotator variability among 12 dermatologists with varying levels of experience on a set of 77 dermoscopic images, showing that the average pairwise XOR dissimilarity (Section 4.3) between annotators was GLYPH&lt;25&gt; 15%, and that in 10% of cases, this value was &gt; 28%. They found more agreement among more experienced dermatologists than less experienced ones. Also, more experienced dermatologists tend to outline tighter borders than less experienced ones. They suggest that the level of agreement among experienced dermatologists could serve as an upper bound for the accuracy achievable by a segmentation algorithm, i.e., if even highly experienced dermatologists disagree on how to classify 10% of an image, it might be unreasonable to expect a segmentation algorithm to agree with more than 90% of any given ground-truth on the same image (Fortina et al., 2012).

Due to the aforementioned variability issues, whenever possible, skin lesion segmentation should be evaluated against multiple expert ground-truths, a good algorithm being one that agrees with the ground-truths at least as well as the expert agree among themselves (Chalana and Kim, 1997). Due to the cost of annotation, however, algorithms are often evaluated against a single ground-truth.

When multiple ground-truths are available, the critical issue is how to employ them. Several approaches have been proposed:

- GLYPH&lt;136&gt; Preferring one of the annotations (e.g., the one by the most experienced expert) and ignoring the others (Celebi et al., 2007a).

<!-- page_break -->

- GLYPH&lt;136&gt; Measuring and reporting the results for each annotator separately (Celebi et al., 2008), which might require non-trivial multivariate analyses if the aim is to rank the algorithms.
- GLYPH&lt;136&gt; Measuring each automated segmentation against all corresponding ground-truths and reporting the average result (Schaefer et al., 2011).
- GLYPH&lt;136&gt; Measuring each automated segmentation against an ensemble ground-truth formed by combining the corresponding groundtruths pixel-wise using a bitwise OR (Garnavi et al., 2011a; Garnavi and Aldeen, 2011), bitwise AND (Garnavi et al., 2011b), or a majority voting (Iyatomi et al., 2006, 2008; Norton et al., 2012).

The ground-truth ensembling process can be generalized using a thresholded probability map (Biancardi et al., 2010). First, all ground-truths for a sample are averaged pixel-wise into a probability map . Then the map is binarized, with the lesion corresponding to pixels greater than or equal to a chosen threshold. The operations of OR AND , , and majority voting, correspond, respectively to thresholds of 1 = n , 1, and ( n GLYPH&lt;0&gt; " = ) 2 n , with n being the number of ground-truths, and " being a small positive constant. AND and OR correspond, respectively, to the tightest and loosest possible contours, with other thresholds leading to intermediate results. While the optimal threshold value is data-dependent, large thresholds focus the evaluation on unambiguous regions, leading to overly optimistic evaluations of segmentation quality (Smyth et al., 1995; Lampert et al., 2016).

The abovementioned approaches fail to consider the di GLYPH&lt;11&gt; erences of experience or performance of the annotators (Warfield and Wells, 2004). More elaborate ground-truth fusion alternatives include shape averaging (Rohlfing and Maurer, 2006), border averaging (Chen and Parent, 1989; Chalana and Kim, 1997), binary label fusion algorithms such as STAPLE (Warfield and Wells, 2004), TESD (Biancardi et al., 2010), and SIMPLE (Langerak et al., 2010), as well as other more recent algorithms (Peng and Li, 2013; Peng et al., 2016, 2017a).

STAPLE (Simultaneous Truth And Performance Level Estimation) has been very influential in medical image segmentation evaluation, inspiring many variants. For each image and its ground-truth segmentations, STAPLE estimates a probabilistic true segmentation through the optimal combination of individual ground-truths, weighting each one by the estimated sensitivity and specificity of its annotator. STAPLE may fail when there are only a few annotators or when their performances vary too much (Langerak et al., 2010; Lampert et al., 2016), a situation addressed by SIMPLE (Selective and Iterative Method for Performance Level Estimation) (Langerak et al., 2010) by iteratively discarding poor quality ground-truths.

Instead of attempting to fuse multiple ground-truths into a single one before employing conventional evaluation metrics, the metrics themselves may be modified to take into account annotation variability. Celebi et al. (2009c) proposed the normalized probabilistic rand index ( NPRI ) (Unnikrishnan et al., 2007), a generalization of the rand index (Rand, 1971). It penalizes segmentation results more (less) in regions where the ground-truths agree (disagree). Fig. 9 illustrates the idea: ground-truths outlined by three experienced dermatologists appear in red, green, and blue, while the automated result appears in black. NPRI does not penalize the automated segmentation in the upper part of the image, where the blue border seriously disagrees with the other two (Celebi et al., 2009c). Despite its many desirable qualities, NPRI has a subtle flaw: it is non-monotonic with the fraction of misclassified pixels (Peserico and Silletti, 2010). Consequently, this index might be unsuitable for comparing poor segmentation algorithms.

<!-- page_break -->

Fig. 9: Sample segmentation results demonstrating inter-annotator disagreements. Note how annotator preferences can a GLYPH&lt;11&gt; ect the manual segmentations, e.g., smooth lesion borders (green), jagged lesion borders (black), oversegmented lesion (blue), etc. Figure taken from Celebi et al. (2009c) with permission.

picture_counter_23 The image shows a close-up of a skin lesion with multiple colored contour lines overlaid on it. The lesion appears dark and irregularly shaped, with the contour lines in blue, green, red, and black outlining different aspects or boundaries of the lesion. This likely represents the application of artificial intelligence techniques in segmenting or analyzing the lesion for diagnostic purposes.


## 4.3. Evaluation Metrics

We can frame the skin lesion segmentation problem as a binary pixel-wise classification task, where the positive and negative classes correspond to the lesion and the background skin, respectively. Suppose that we have an input image and its corresponding segmentations: an automated segmentation ( AS ) produced by a segmentation algorithm and a manual segmentation ( MS ) outlined by a human expert. We can formulate a number of quantitative segmentation evaluation measures based on the concepts of true positive , false negative , false positive , and true negative , whose definitions are given in Table 2. In this table, actual and detected pixels refer to any given pixel in the MS and the corresponding pixel in the AS , respectively.

Table 2: Definitions of true positive, false negative, false positive, and true negative pixels in the context of skin lesion segmentation.


## Detected Pixel

|        |                         | Lesion ( + )   | Background ( GLYPH<0> )   |
|--------|-------------------------|----------------|---------------------------|
| Actual | Lesion ( + )            | True Positive  | False Negative            |
| Pixel  | Background ( GLYPH<0> ) | False Positive | True Negative             |

For a given pair of automated and manual segmentations, we can construct a 2 GLYPH&lt;2&gt; 2 confusion matrix (aka a contingency table (Pearson, 1904; Miller and Nicely, 1955)) C = GLYPH&lt;16&gt; TP FN FP TN GLYPH&lt;17&gt; , where TP FN FP , , , and TN denote the numbers of true positives, false negatives, false positives, and true negatives, respectively. Clearly, we have N = TP + FN + FP + TN , where N is the number of pixels in either image. Based on these quantities, we can define a variety of scalar similarity measures to quantify the accuracy of segmentation (Baldi et al., 2000; Japkowicz and Shah, 2011; Taha and Hanbury, 2015):

- GLYPH&lt;136&gt; Sensitivity ( SE ) and Specificity ( SP ) (Kahn, 1942; Yerushalmy, 1947; Binney et al., 2021): SE = TP TP + FN &amp; SP = TN TN + FP
- GLYPH&lt;136&gt; Precision ( PR ) and Recall ( RE ) (Kent et al., 1955): PR = TP TP + FP &amp; RE = TP TP + FN
- GLYPH&lt;136&gt; Accuracy ( AC ) = TP + TN TP + FN + FP + TN

<!-- page_break -->

- GLYPH&lt;136&gt; F-measure ( F ) (van Rijsbergen, 1979) = 2 j AS \ MS j j AS j + j MS j = 2 GLYPH&lt;1&gt; PR GLYPH&lt;1&gt; RE PR + RE = 2 TP 2 TP + FP + FN
- GLYPH&lt;136&gt; G-mean ( GM ) (Kubat et al., 1998) = p SE GLYPH&lt;1&gt; SP
- GLYPH&lt;136&gt; Balanced Accuracy ( BA ) (Chou and Fasman, 1978) = SE + SP 2
- GLYPH&lt;136&gt; Jaccard index ( J ) (Jaccard, 1901) = j AS \ MS j j AS [ MS j = TP TP + FN + FP
- GLYPH&lt;136&gt; Matthews Correlation Coe GLYPH&lt;14&gt; cient ( MCC ) (Matthews, 1975) = TP GLYPH&lt;1&gt; TN GLYPH&lt;0&gt; FP GLYPH&lt;1&gt; FN p ( TP + FP )( TP + FN )( TN + FP )( TN + FN )

For each similarity measure, the higher the value, the better the segmentation. Except for MCC , all of these measures have a unit range, that is, [0 ; 1]. The [ GLYPH&lt;0&gt; 1 1] range of ; MCC can be mapped to [0 1] by adding one to it and then dividing by two. Each of ; these unit-range similarity measures can then be converted to a unit-range dissimilarity measure by subtracting it from one. Note that there are also dissimilarity measures with no corresponding similarity formulation. A prime example is the well-known XOR measure (Hance et al., 1996) defined as follows:

$$X O R = \frac { | A S \oplus M S | } { | M S | } = \frac { | ( A S \cup M S ) - ( A S \cap M S ) | } { | M S | } = \frac { F P + F N } { T P + F N }.$$

It is essential to notice that di GLYPH&lt;11&gt; erent evaluation measures capture di GLYPH&lt;11&gt; erent aspects of a segmentation algorithm's performance on a given dataset, and thus there is no universally applicable evaluation measure (Japkowicz and Shah, 2011). This is why most studies employ multiple evaluation measures in an e GLYPH&lt;11&gt; ort to perform a comprehensive performance evaluation. Such a strategy, however, complicates algorithm comparisons, unless one algorithm completely dominates the others with respect to all adopted evaluation measures.

Based on their observation that experts tend to avoid missing parts of the lesion in their manual borders, Garnavi et al. (2011a) argue that true positives have the highest importance in the segmentation of skin lesion images. The authors also assert that false positives (background pixels incorrectly identified as part of the lesion) are less important than false negatives (lesion pixels incorrectly identified as part of the background). Accordingly, they assign a weight of 1 5 to : TP to signify its overall importance. Furthermore, in measures that involve both FN and FP (e.g., AC F , , and XOR ), they assign a weight of 0.5 to FP to emphasize its importance over FN . Using these weights, they construct a weighted performance index , which is an arithmetic average of six commonly used measures, namely SE SP PR AC F , , , , , and (unit complement of) XOR . This scalar evaluation measure facilitates comparisons among algorithms.

In a follow-up study, Garnavi and Aldeen (2011) parameterize the weights of TP FN FP , , , and TN in their weighted performance index and then use a constrained non-linear program to determine the optimal weights. They conduct experiments with five segmentation algorithms on 55 dermoscopic images. They conclude that the optimized weights not only lead to automated algorithms that are more accurate against manual segmentations, but also diminish the di GLYPH&lt;11&gt; erences among those algorithms.

We make the following key observations about the popular evaluation metrics and how they have been used in the skin lesion segmentation literature:

<!-- page_break -->

- GLYPH&lt;136&gt; Historically, AC has been the most popular evaluation measure owing to its simple and intuitive formulation. However, this measure tends to favor the majority class, leading to overly optimistic performance estimates in class-imbalanced domains. This drawback prompted the development of more elaborate performance evaluation measures, including GM BA , , and MCC .
- GLYPH&lt;136&gt; SE and SP are especially popular in medical domains, tracing their usage in serologic test reports in the early 1900s (Binney et al., 2021). SE (aka True Positive Rate ) quantifies the accuracy on the positive class, whereas SP (aka True Negative Rate ) quantifies the accuracy on the negative class. These measures are generally used together because it is otherwise trivial to maximize one at the expense of the other (an automated border enclosing the corresponding manual border will attain a perfect SE , whereas in the opposite case, we will have a perfect SP ). Unlike AC , they are suitable for class-imbalanced domains. BA and GM combine these measures into a single evaluation measure through arithmetic and geometric averaging, respectively. Unlike AC , these composite measures are suitable for class-imbalanced domains (Luque et al., 2020).
- GLYPH&lt;136&gt; PR is the proportion of examples assigned to the positive class that actually belongs to the positive class. RE is equivalent to SE PR . and RE are typically used in information retrieval applications, where the focus is solely on relevant documents (positive class). F combines these measures into a single evaluation measure through harmonic averaging. This composite measure, however, is unsuitable for class-imbalanced domains (Zou et al., 2004; Chicco and Jurman, 2020; Luque et al., 2020).
- GLYPH&lt;136&gt; MCC is equivalent to the phi coe GLYPH&lt;14&gt; cient , which is simply the Pearson correlation coe GLYPH&lt;14&gt; cient applied to binary data (Chicco and Jurman, 2020). MCC values fall within the range of [ GLYPH&lt;0&gt; 1 1] with ; GLYPH&lt;0&gt; 1 and 1 indicating perfect misclassification and perfect classification, respectively, while 0 indicating a classification no better than random (Matthews, 1975). Although it is biased to a certain extent (Luque et al., 2020; Zhu, 2020), this measure appears to be suitable for class-imbalanced domains (Boughorbel et al., 2017; Chicco and Jurman, 2020; Luque et al., 2020).
- GLYPH&lt;136&gt; J (aka Intersection over Union (Jaccard, 1912)) and F (aka Dice coe GLYPH&lt;14&gt; cient aka Sørensen-Dice coe GLYPH&lt;14&gt; cient (Dice, 1945; Sørensen, 1948)) are highly popular in medical image segmentation (Crum et al., 2006). These measures are monotonically related as follows: J = F = (2 GLYPH&lt;0&gt; F ) and F = 2 J = (1 + J ). Thus, it makes little sense to use them together. There are two major di GLYPH&lt;11&gt; erences between these measures: (i) (1 GLYPH&lt;0&gt; J ) is a proper distance metric, whereas (1 GLYPH&lt;0&gt; F ) is not (it violates the triangle inequality). (ii) It can be shown (Zijdenbos et al., 1994) that if TN is su GLYPH&lt;14&gt; ciently large compared to TP FN , , and FP , which is common in skin lesion segmentation, F becomes equivalent to Cohen's kappa (Cohen, 1960), which is a chance-corrected measure of inter-observer agreement.
- GLYPH&lt;136&gt; Among the seven composite evaluation measures given above, AC GM BA , , , and MCC are symmetric, that is, they are invariant to class swapping, while F J , , and XOR are asymmetric.
- GLYPH&lt;136&gt; XOR is similar to False Negative Rate , that is, the unit complement of SE , with the exception that XOR has an extra additive TN term in its numerator. While XOR values are guaranteed to be nonnegative, they do not have a fixed upper bound, which makes aggregations of this measure di GLYPH&lt;14&gt; cult. XOR is also biased against small lesions (Celebi et al., 2009c). Nevertheless, owing to its intuitive formulation, XOR was popular in skin lesion segmentation until about 2015 (Celebi et al., 2015b).

<!-- page_break -->

- GLYPH&lt;136&gt; The 2016 and 2017 ISIC Challenges (Gutman et al., 2016; Codella et al., 2018) adopted five measures: AC SE SP F , , , , and J , with the participants ranked based on the last measure. The 2018 ISIC Challenge (Codella et al., 2019) featured a thresholded Jaccard index , which returns the same value as the original J if the value is greater than or equal to a predefined threshold and zero otherwise. Essentially, this modified index considers automated segmentations yielding J values below the threshold as complete failures. The challenge organizers set the threshold equal to 0 65 based on an earlier study (Codella et al., 2017) : that determined the average pairwise J similarities among the manual segmentations outlined by three expert dermatologists. Since the majority of papers in this survey (168 out of 177 papers) use the ISIC datasets (Fig. 3), we list the J for all the papers in Table 3 wherever it has been reported in the corresponding papers. For papers that did not report J and instead reported F , we list the computed J based on F and denote it with an asterisk.
- GLYPH&lt;136&gt; Some of the aforementioned measures (i.e., GM and BA ) have not been used in a skin lesion segmentation study yet.
- GLYPH&lt;136&gt; The evaluation measures discussed above are all region-based and thus fairly insensitive to border irregularities (Lee et al., 2003), i.e., indentations, and protrusions along the border. Boundary-based evaluation measures (Taha and Hanbury, 2015) have not been used in the skin lesion segmentation literature much except for the symmetric Hausdor GLYPH&lt;11&gt; metric (Silveira et al., 2009), which is known to be sensitive to noise (Huttenlocher et al., 1993) and biased in favor of small lesions (Bogo et al., 2015).


## 5. Discussion and Future Research

In this paper, we presented an overview of DL -based skin lesion segmentation algorithms. A lot of work has been done in this field since the first application of CNN s on these images in 2015 (Codella et al., 2015). In fact, the number of skin lesion segmentation papers published over the past 8 years (2015-2022) is more than thrice those published over the previous 17 years (1998-2014) (Celebi et al., 2015b).

However, despite the large body of work, skin lesion segmentation remains an open problem, as evidenced by the ISIC 2018 Skin Lesion Segmentation Live Leaderboard (ISIC, 2018). The live leaderboard has been open and accepting submissions since 2018, and even after the permitted usage of external data, the best thresholded Jaccard index (the metric used to rank submissions) is 83 6%. Additionally, the release of the : HAM10000 lesion segmentations (Tschandl et al., 2020; ViDIR Dataverse, 2020) in 2020 shows that progressively larger skin lesion segmentation datasets continue to be released. We believe that the following aspects of skin lesion segmentation via deep learning are worthy of future work:

- GLYPH&lt;136&gt; Mobile dermoscopic image analysis: With the availability of various inexpensive dermoscopes designed for smartphones, mobile dermoscopic image analysis is of great interest worldwide, especially in regions where access to dermatologists is limited. Typical DL -based image segmentation algorithms have millions of weights. In addition, classical CNN architectures are known to exhibit di GLYPH&lt;14&gt; culty dealing with certain image distortions such as noise and blur (Dodge and Karam, 2016), and DL-based skin lesion diagnosis models have been demonstrated to be susceptible to similar artifacts: various kinds of noise and blur, brightness and contrast changes, dark corners (Maron et al., 2021b), bubbles, rulers, ink markings, etc. (Katsch

<!-- page_break -->

et al., 2022). Therefore, the current dermoscopic image segmentation algorithms may not be ideal for execution on typically resource-constrained mobile and edge devices, needed for patient privacy so that uploading skin images to remote servers is avoided. Leaner DL architectures, e.g., MobileNet (Howard et al., 2019), Shu GLYPH&lt;15&gt; eNet (Zhang et al., 2018), E GLYPH&lt;14&gt; cientNet (Tan and Le, 2019), MnasNet (Tan et al., 2019a), and UNeXt (Valanarasu and Patel, 2022), should be investigated in addition to the robustness of such architectures with respect to image noise and blur.

- GLYPH&lt;136&gt; Datasets: To train more accurate and robust deep neural segmentation architectures, we need larger, more diverse, and more representative skin lesion datasets with multiple manual segmentations per image. Additionally, as mentioned in Section 2.1, several skin lesion image classification datasets do not have the corresponding lesion mask annotations, and given their popularity in skin image analysis tasks, they may be good targets for manual delineations. For example, the PAD-UFES-20 dataset (Pacheco et al., 2020) consists of clinical images of skin lesions captured using smartphones, and obtaining groundtruth segmentations on this dataset would help advance skin image analysis on mobile devices. Additionally, a recent study conducted by Daneshjou et al. (2021a) found that as little as 10% of the AI-based studies for dermatological diagnosis included skin tone information for at least one dataset used, and that several studies included little to no images of darker skin tones, underlining the need to curate datasets with diverse skin tones.
- GLYPH&lt;136&gt; Collecting segmentation annotations: At the time of this writing, the ISIC Archive contains over 71 000 publicly available ; images. Considering that the largest public dermoscopic image set contained a little over 1 ; 000 images about six years ago, we have come a long way. The more pressing problem now is the lack of manual segmentations for most of these images. Since manual segmentation by medical experts is laborious and costly, crowdsourcing techniques (Kovashka et al., 2016) could be explored to collect annotations from non-experts. Experts could then revise these initial annotations, or methods that tackle the problem of annotation noise (Mirikharaji et al., 2019; Karimi et al., 2020; Li et al., 2021a) could be explored. Note that the utility of crowdsourcing in medical image annotation has been demonstrated in multiple studies (FoncubiertaRodriguez and Muller, 2012; Gurari et al., 2015; Sharma et al., 2017; Goel et al., 2020). Additionally, keeping in mind the time-consuming nature of manual supervised annotation, an alternative is to use weakly-supervised annotation, e.g., bounding-box annotations (Dai et al., 2015; Papandreou et al., 2015), which are much less time-consuming to collect. For example, for several large skin lesion image datasets that do not have any lesion mask annotations (see Section 2.1), boundingbox lesion annotations can be obtained more easily than dense pixel-level segmentation annotations. In addition, weaklysupervised annotation (Bearman et al., 2016; Tajbakhsh et al., 2020b; Roth et al., 2021; En and Guo, 2022) is more amenable to crowdsourcing (Maier-Hein et al., 2014; Rajchl et al., 2016; Papadopoulos et al., 2017; Lin et al., 2019), especially for non-experts.
- GLYPH&lt;136&gt; Handling multiple annotations per image: If the skin lesion image dataset at hand contains multiple manual segmentations per image, one should consider either using an algorithm such as STAPLE (Warfield and Wells, 2004) for fusing the manual segmentations (see Section 4), or relying on learning-based approaches, either through variants of STAPLE adapted for DL -based segmentation (Kats et al., 2019; Zhang et al., 2020b), or other methods (Mirikharaji et al., 2021; Lemay et al., 2022). Such a fusion algorithm can also be used to build an ensemble of multiple automated segmentations.

<!-- page_break -->

36

- GLYPH&lt;136&gt; Supervised segmentation evaluation measures: Supervised segmentation evaluation measures popular in the skin image analysis literature (see Section 4.3) are often region-based, pair-counting measures. Other region-based measures, such as information-theoretic measures (e.g., mutual information, variation of information, etc.) as well as boundary-based measures e.g., Hausdor GLYPH&lt;11&gt; distance (Taha and Hanbury, 2015) should be explored as well.
- GLYPH&lt;136&gt; Unsupervised segmentation and unsupervised segmentation evaluation: Current DL -based skin lesion segmentation algorithms are mostly based on supervised learning, as shown in a supervision-level breakdown of the surveyed works (Fig. 5), meaning that these algorithms require manual segmentations for training segmentation prediction models. Nearly all of these segmentation studies employ supervised segmentation evaluation, meaning that they also require manual segmentations for testing. Due to the scarcity of annotated skin lesion images, it may be beneficial to investigate unsupervised DL (Ji et al., 2019) as well as unsupervised segmentation evaluation (Chabrier et al., 2006; Zhang et al., 2008).
- GLYPH&lt;136&gt; Systematic evaluations: Systematic evaluations that have been performed for skin lesion classification (Valle et al., 2020; Bissoto et al., 2021; Perez et al., 2018) are, so far, nonexistent in the skin lesion segmentation literature. For example, statistical significance analysis are conducted on the results of a few prior studies in skin lesion segmentation, e.g., Fortina et al. (2012).
- GLYPH&lt;136&gt; Fusion of hand-crafted and deep features: Can we integrate the deep features extracted by DL models and hand-crafted features synergistically? For example, exploration of shape and appearance priors of skin lesions that may be beneficial to incorporate, via loss terms (Nosrati and Hamarneh, 2016; El Jurdi et al., 2021; Ma et al., 2021), in deep learning models for skin lesion segmentation, similar to star-shape (Mirikharaji and Hamarneh, 2018) and boundary priors (Wang et al., 2021a).
- GLYPH&lt;136&gt; Loss of spatial resolution: The use of repeated subsampling in CNN s leads to coarse segmentations. Various approaches have been proposed to minimize the loss of spatial resolution, including fractionally-strided convolution (or deconvolution) (Long et al., 2015), atrous (or dilated) convolution (Chen et al., 2017a), and conditional random fields (Krahenbuhl and Koltun, 2011). More research needs to be conducted to determine appropriate strategies for skin lesion segmentation that e GLYPH&lt;11&gt; ectively minimize or avoid the loss of spatial resolution.
- GLYPH&lt;136&gt; Hyperparameter tuning: Compared to traditional machine learning classifiers (e.g., nearest neighbors, decision trees, and support vector machines), deep neural networks have a large number of hyperparameters related to their architecture, optimization, and regularization. An average CNN classifier has about a dozen or more hyperparameters (Bengio, 2012) and tuning these hyperparameters systematically is a laborious undertaking. Neural architecture search is an active area of research (Elsken et al., 2019), and some of these model selection approaches have already been applied to semantic segmentation (Liu et al., 2019a) and medical image segmentation (Weng et al., 2019).
- GLYPH&lt;136&gt; Reproducibility of results: Kapoor and Narayanan (2022) define research in ML-based science to be reproducible if the associated datasets and the code are publicly available and if there are no problems with the data analysis, where problems include the lack of well-defined training and testing partitions of the dataset, leakage across dataset partitions, features selection using the entire dataset instead of only the training partition, etc. Since several skin lesion segmentation datasets

<!-- page_break -->

picture_counter_24 The image is a bar plot showing the dataset size over the years for "Clinical" and "Dermoscopy" categories. The years range from 2012 to 2020, with the dataset size on the y-axis. "Clinical" is represented by cyan bars, and "Dermoscopy" is represented by dark blue bars. The dataset size for "Dermoscopy" significantly increases in 2020, reaching over 10,000, while "Clinical" remains relatively small throughout the years.

Year

Fig. 10: Number of skin lesion images with ground-truth segmentation maps per year categorized based on modality. It is evident that while the number of dermoscopic skin lesion images has been constantly on the rise, the number of clinical images has remained unchanged for the past several years.

come with standardized partitions (Table 1), sharing of the code can lead to more reproducible research (Colliot et al., 2022), with the added benefit to researchers who release their code to be cited significantly more (Vandewalle, 2012). In our analysis, we found that only 38 of the 177 surveyed papers (21 47%) had publicly accessible code (Table 3), a proportion similar to a : smaller-scale analysis by Renard et al. (2020) for medical image segmentation. Another potential assessment of a method's generalization performance is its evaluation on a common held-out test set, where the ground truth segmentation masks are private, and users submit their test predictions to receive a performance assessment. For example, the ISIC 2018 dataset's test partition is available through a live leaderboard (ISIC, 2018), but it is rarely used. We found that out of 71 papers published in 2021 and 2022 included in this survey, 36 papers reported results on the ISIC 2018 dataset, but only 1 paper (Saini et al., 2021) used the online submission platform for evaluation.

- GLYPH&lt;136&gt; Research on clinical images: Another limitation is the limited number of benchmark datasets of clinical skin lesion images with expert pixel-level annotations. Fig. 10 shows that while the number of dermoscopic image datasets with ground-truth segmentation masks has been increasing over the last few years, only a few datasets with clinical images are available. In contrast to dermoscopic images requiring a special tool that is not always utilized even by dermatologists (Engasser and Warshaw, 2010), clinical images captured by digital cameras or smartphones have the advantage of easy accessibility, which can be utilized to evaluate the priority of patients by their lesion severity level, i.e., triage patients. As shown in Fig. 3 and Table 3, most of the deep skin lesion segmentation models are trained and evaluated on dermoscopic images, primarily because of the lack of large-scale clinical skin lesion image segmentation datasets (Table 1), leaving the need to develop automated tools for non-specialists unmet.
- GLYPH&lt;136&gt; Research on total body images: While there has been some research towards detecting and tracking skin lesions over time

<!-- page_break -->

in 2D wide-field images (Mirzaalian et al., 2016; Li et al., 2017; Korotkov et al., 2019; Soenksen et al., 2021; Huang et al., 2022) and in 3D total body images (Bogo et al., 2014; Zhao et al., 2022a), simultaneous segmentation of skin lesions from total body images would help with early detection of melanoma (Halpern, 2003; Hornung et al., 2021), thus improving patient outcomes.

- GLYPH&lt;136&gt; E ect on downstream tasks: End-to-end systems have been proposed for skin images analysis tasks that directly learn the final GLYPH&lt;11&gt; tasks (e.g., predicting the diagnosis (Kawahara et al., 2019) or the clinical management decisions (Abhishek et al., 2021) of skin lesions), and these approaches present a number of advantages such as computational e GLYPH&lt;14&gt; ciency and ease of optimization. On the other hand, skin lesion diagnosis pipelines have been shown to benefit from the incorporation of prior knowledge, specifically lesion segmentation masks (Yan et al., 2019). Therefore, it is worth investigating how lesion segmentation, often an intermediate step in the skin image analysis pipeline, a GLYPH&lt;11&gt; ects the downstream dermatological tasks.
- GLYPH&lt;136&gt; From binary to multi-class segmentation: While the existing work in skin lesion segmentation is mainly binary segmentation, future work may explore multi-class settings. For example, automated detection and delineation of clinical dermoscopic features (e.g., globules, streaks, pigment networks) within a skin lesion may lead to superior classification performance. Further, dermoscopic feature extraction, a task in the ISIC 2016 (Gutman et al., 2016) and 2017 (Codella et al., 2018) challenges, can be formulated as a multi-class segmentation problem (Kawahara and Hamarneh, 2018). The multiclass formulation can then be addressed by DL models, and can be used either as an intermediate step for improving skin lesion diagnosis or used directly in diagnosis models for regularizing attention maps (Yan et al., 2019). Similarly, multi-class segmentation scenarios may also include multiple skin pathologies on one subject, especially in images with large fields of view, or segmentation of the skin, the lesion(s), and the background, especially in in-the-wild images with diverse backgrounds, such as those in the Fitzpatrick17k dataset (Groh et al., 2021).
- GLYPH&lt;136&gt; Transferability of models: As the majority of skin lesion datasets are from fair-skinned patients, the generalizability of deep models to populations with diverse skin complexions is questionable. With the emergence of dermatological datasets with diverse skin tones (Groh et al., 2021; Daneshjou et al., 2021b) and methods for diagnosing pathologies fairly (Bevan and Atapour-Abarghouei, 2022; Wu et al., 2022c; Pakzad et al., 2022; Du et al., 2022), it is important to assess the transferability of DL -based skin lesion segmentation models to datasets with diverse skin tones.