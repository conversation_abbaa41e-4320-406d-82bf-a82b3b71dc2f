## Experimental results

This section presents the experimental results of the proposed framework to evaluate its performance and study the effect of different enhancements applied to the X-ray images on the performance of the CNN classification model.


## Experimental setup

Keras Python deep learning library on top of TensorFlow was utilized for implementing CNN models on a machine with the following specification; an   Intel  Core™ i7 CPU@ 3.6 GHz with 32 GB RAM and a Titan × ® Pascal Graphics Processing Unit (GPU). Extensive experiments were carried out to obtain the best settings of the CNN models that achieve the best possible results. It is worth noting that the pre-processing for enhancing the images has been carried out using   MATLAB 18 software. ®

Both the full and segmented datasets with their enhanced versions have been used to train VGG19 and EfficientNetB0 CNN pre-trained models. The training was carried on with Adam optimizer, learning rate of 0.001, batch size of 32, and the number of epochs (10-30) epochs, SoftMax classifier. The fine-tuned pre-trained models were used for feature extraction; therefore, the weights of the pre-trained models were frozen, and they were not updated during the training to maintain ImageNet's initial weights. The top layers were fine-tuned to adjust the

Figure 4. The framework of the used methodology for Chest X-ray images classification.

picture_counter_23 The image illustrates a workflow for medical image analysis using artificial intelligence techniques. It shows two main sections: "Original & enhancements" and "Segmented." The original chest X-ray images undergo various enhancements, including HE (Histogram Equalization), CLAHE (Contrast Limited Adaptive Histogram Equalization), and Complement. These enhanced images are then segmented using a mask. Both the original and segmented images are fed into two different neural networks, VGG19 and EfficientNetB0, indicating the use of transfer learning for disease diagnosis. The image includes arrows connecting each step, showing the flow of data through the process.

<!-- page_break -->

network according to the used chest X-ray data and to the current problem output which is (2/4) rather than 1000 in the ImageNet data. To avoid overfitting, a dropout of 0.3 was applied in the fully connected layers. Figures 5 and 6 illustrate the fine-tuned top layers based on VGG19 and EfficientNetB0 respectively for binary classification.


## Performance evaluation

A benchmark dataset was employed to validate the performance of the proposed framework. For binary classification, a set of 1600 X-ray images (800 of each class) have been used to train CNN models using transfer learning. The dataset has been divided into three subsets training, validation, and test sets. The training set is used for learning the model and adjusting the parameters. The validation set is to test the model during the training phase and fine-tune the parameters. The test set is to evaluate the trained model. The division was done as 400 samples (25%) of the used X-ray images were selected randomly for testing (200 images for each class), and the remaining 75% samples were split again into training and validation splits (80-20%). For 4-classes classification, a set of 3200 X-ray images (800 of each class) have been used. Before training CNN models, different preprocessing steps were implemented to enhance the images of both full and segmented lungs chest X-ray images to investigate the classification performance of the CNN models using the different versions.

The following metrics were used for the evaluation of the different CNN models trained using various dataset versions:

$$Sensitivity/Recall(%) = \frac { T P } { T P + F N }$$

$$\text{Precision} ( \%) = \frac { T p } { T P + F P }$$

$$\text{Specificity} ( \% ) = \frac { T N } { T N + FP }$$

$$\text{Accuracy} ( \%) = \frac { T P + T N } { T P + F P + T N + F N }$$

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_\_\_\_\_

==============================================================

| dense (Dense)     | (None, 1024)   |   25691136 |
|-------------------|----------------|------------|
| dense_1 (Dense)   | (None, 512)    |     524800 |
| dense_2 (Dense)   | (None, 256)    |     131328 |
| dropout (Dropout) | (None, 256)    |          0 |
| dense_3 (Dense)   | (None, 2)      |        514 |

Total params: 39,292,738

Trainable params: 26,347,778

Non-trainable params: 12,944,960

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_\_\_\_\_\_\_\_

Figure 5. Fine-tuned top layers based on VGG19 pre-training model.

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_

dense (Dense)                  (None, 1024)         5243904

dense\_1 (Dense)                (None, 512)          524800

dense\_2 (Dense)                (None, 256)          131328

dropout (Dropout)

(None, 256)          0

dense\_3 (Dense)                (None, 2)            514

=================================================================

Total params: 9,534,117

Trainable params: 5,900,546

Non-trainable params: 3,633,571

\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_\_ \_

Figure 6. Fine-tuned top layers based on EfficientNetB0 pre-training model.

Vol.:(0123456789)

<!-- page_break -->

Vol:.(1234567890)

$$F l s c o r e = \frac { 2 T P } { 2 T P + F P + F N }$$

$$( 5 )$$

where TP is a true-positive value, FP is a false-positive value, TN is a true-negative value and FN is a falsenegative value.


## Results

Tables 2 and 3 show the results of training VGG19 using the original and different enhanced versions of full X-ray images and of segmented versions respectively. As it is shown in Table 2, applying different image enhancement techniques has improved the performance of the classification model. The accuracy of classification for the model trained using the original images' version was 0.913, however, it has been improved for the enhanced versions to reach 0.94, 0.95, and 0.9475 for histeq, CLAHE, and complement respectively. The detailed results including TP , TN, FP, FN, sensitivity, specificity, precision, F1 score, test accuracy, and AUC of the proposed full X-ray images using VGG19 are shown in Table 2. The performance of the model trained using the CLAHE version is the best.

Regarding the segmented versions, as it is shown in Table 3 the accuracy of classification of the model trained using the original segmented dataset version was 0.887. However, the accuracy has been improved for the enhanced segmented dataset versions to reach 0.91 using Histeq techniques, 0.9049 for CLAHE and 0.9075 for the complement version. It is clear that the accuracies using different enhanced versions are close to each other, and they are better than that of the original segmented version. The detailed results using the different metrics are shown in Table 3.

Table 4 shows the results of training EfficientNetB0 using the original and different enhanced versions of full X-ray images. The accuracy of classification using the original full images' version was 0.915, it reached 0.94, 0.938, and 0.94 for histeq, CLAHE, and complement versions respectively. The accuracies for the models trained using different enhanced versions are better than that of the original version. The detailed results using the different metrics are shown in Table 4.

Regarding the segmented versions, as shown in Table 5 the accuracy of the classification for the EfficientNetB0 model trained using the original segmented lung dataset version was 0.885. However, the accuracy has been improved to 0.905, 0.905, and 0.9075 for Histeq, CLAHE and complement versions respectively. As with VGG19, the accuracies of training EfficientNetB0 using different enhanced segmented versions are close to each other, but they are all better than that of the original segmented version. The detailed results using the different metrics are shown in Table 5.

It is clear that the performance of all enhanced versions is better than that of their associated original version using either VGG19 or EfficientNetB0 models and for both full and segmented versions. By comparing the results

| Dataset    |   Tn |   Fp |   Tp |   Fn |   Sensitivity |   Specificity |   Precision |   F1 score |   Test acc |    AUC |
|------------|------|------|------|------|---------------|---------------|-------------|------------|------------|--------|
| Original   |  170 |   30 |  195 |    5 |         0.975 |         0.85  |     0.8667  |     0.9176 |     0.913  | 0.9125 |
| Histeq     |  192 |    8 |  184 |   16 |         0.92  |         0.96  |     0.9583  |     0.9388 |     0.94   | 0.94   |
| CLAHE      |  188 |   12 |  192 |    8 |         0.96  |         0.94  |     0.9412  |     0.9505 |     0.95   | 0.95   |
| Complement |  187 |   13 |  192 |    8 |         0.96  |         0.935 |     0.93659 |     0.9482 |     0.9475 | 0.9475 |

Table 2. The results of applying VGG19 to original and different enhanced versions of the used dataset.

Table 3. The results of applying VGG19 to original and different enhanced versions of the used dataset after segmentation.

| Dataset              |   Tn |   Fp |   Tp |   Fn |   Sensitivity |   Specificity |   Precision |   F1 score |   Test acc |    AUC |
|----------------------|------|------|------|------|---------------|---------------|-------------|------------|------------|--------|
| Segmented original   |  157 |   43 |  198 |    2 |         0.99  |         0.785 |      0.8216 |     0.898  |      0.887 | 0.8875 |
| Segmented Histeq     |  173 |   27 |  191 |    9 |         0.955 |         0.865 |      0.876  |     0.9139 |      0.91  | 0.91   |
| Segmented CLAHE      |  173 |   27 |  189 |   11 |         0.945 |         0.865 |      0.875  |     0.9086 |      0.905 | 0.9049 |
| Segmented complement |  169 |   31 |  194 |    6 |         0.97  |         0.845 |      0.862  |     0.9129 |      0.908 | 0.9075 |

Table 4. The results of applying EfficientNetB0 to original and different enhanced versions of the used dataset.

| Dataset    |   Tn |   Fp |   Tp |   Fn |   Sensitivity |   Specificity |   Precision |   F1 score |   Test acc |    AUC |
|------------|------|------|------|------|---------------|---------------|-------------|------------|------------|--------|
| Original   |  173 |   27 |  193 |    7 |         0.965 |         0.865 |      0.877  |    0.91905 |      0.915 | 0.915  |
| Histeq     |  192 |    8 |  184 |   16 |         0.92  |         0.96  |      0.9583 |    0.9387  |      0.94  | 0.94   |
| CLAHE      |  194 |    6 |  181 |   19 |        90.5   |         0.97  |      0.9679 |    0.9354  |      0.938 | 0.9375 |
| Complement |  193 |    7 |  183 |   17 |         0.915 |         0.965 |      0.963  |    0.9385  |      0.94  | 0.94   |

<!-- page_break -->

Table 5. The results of applying EfficientNetB0 to original and different enhanced versions of the used dataset after segmentation.

| Dataset              |   Tn |   Fp |   Tp |   Fn |   Sensitivity |   Specificity |   Precision |   F1 score |   Test acc |    AUC |
|----------------------|------|------|------|------|---------------|---------------|-------------|------------|------------|--------|
| Segmented original   |  160 |   40 |  194 |    6 |         0.97  |         0.8   |      0.829  |     0.894  |     0.885  | 0.885  |
| Segmented Histeq     |  181 |   19 |  181 |   19 |         0.905 |         0.905 |      0.905  |     0.905  |     0.905  | 0.905  |
| Segmented CLAHE      |  183 |   17 |  179 |   21 |         0.895 |         0.915 |      0.9133 |     0.904  |     0.905  | 0.905  |
| Segmented complement |  174 |   26 |  189 |   11 |         0.945 |         0.87  |      0.879  |     0.9108 |     0.9075 | 0.9075 |

of the full X-ray images and segmented images using the same CNN model, reductions in the performance of the CNN models trained using the segmented datasets rather than those trained using full-image datasets were observed. The models built using full images achieved better performance in general. The reason for that might be that the full images may have more details outside the lung region in the surroundings region that contribute to the classification and help to improve the performance.

Regarding the comparison of the used 2 CNNs models, the results of the two models are close to each other, however, the results of VGG19 are a bit better than those of EfficientNetB0. The best achieved performance is of the VGG19 model trained using the CLAHE version of full X-ray images for binary classification of chest X-ray images into COVID-19 or normal. It achieved a sensitivity of 0.96, specificity of 0.94, precision of 0.9412, F1 score of 0.9505 and accuracy of 0.95. Figures 7, 8, 9 and 10 show the training and validation accuracies, the training and validation losses, the training and test ROC curve and the confusion matrix of that best achieved model respectively.