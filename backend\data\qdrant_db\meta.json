{"collections": {"medical_assistance_rag": {"vectors": {"dense": {"size": 1536, "distance": "<PERSON><PERSON>e", "hnsw_config": null, "quantization_config": null, "on_disk": null, "datatype": null, "multivector_config": null}}, "shard_number": null, "sharding_method": null, "replication_factor": null, "write_consistency_factor": null, "on_disk_payload": null, "hnsw_config": null, "wal_config": null, "optimizers_config": null, "init_from": null, "quantization_config": null, "sparse_vectors": {"sparse": {"index": {"full_scan_threshold": null, "on_disk": false, "datatype": null}, "modifier": null}}, "strict_mode_config": null}}, "aliases": {}}