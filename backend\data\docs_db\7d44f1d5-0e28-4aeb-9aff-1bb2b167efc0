## 2. Input Data

Obtaining data in su GLYPH&lt;14&gt; cient quantity and quality is often a significant obstacle to developing e GLYPH&lt;11&gt; ective segmentation models. State-of-the-art segmentation models have a huge number of adjustable parameters that allow them to generalize well, provided they are trained on massive labeled datasets (<PERSON> et al., 2017; <PERSON><PERSON><PERSON> et al., 2020). Unfortunately, skin lesion datasets-like most medical image datasets (<PERSON><PERSON><PERSON> et al., 2021)-tend to be small (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2019) due to issues such as copyright, patient privacy, acquisition and annotation cost, standardization, and scarcity of many pathologies of interest. The two most common modalities used in the training of skin lesion segmentation models are clinical images , which are close-ups of the lesions acquired using conventional cameras, and dermoscopic images , which are acquired using dermoscopy, a non-invasive skin imaging through optical magnification, and either liquid immersion and low angle-of-incidence lighting, or cross-polarized lighting. Dermoscopy eliminates skin surface reflections (<PERSON><PERSON> et al., 2002), reveals subsurface skin structures, and allows the identification of dozens of morphological features such as atypical pigment networks, dots globules, streaks, blue-white areas, and / blotches (<PERSON><PERSON><PERSON> et al., 2003).

Annotation is often the greatest barrier for increasing the amount of data. Objective evaluation of segmentation often requires laborious region-based annotation , in which an expert manually outlines the region where the lesion (or a clinical feature) appears in

3 Arxiv Sanity Preserver : https://www.arxiv-sanity-lite.com/search?q=segmentation+skin+melanoma+deep+learning+convolution+

<!-- page_break -->

the image. By contrast, textual annotation may involve diagnosis (e.g., melanoma, carcinoma, benign nevi), presence absence score / / of dermoscopic features (e.g., pigment networks, blue-white areas, streaks, globules), diagnostic strategy (e.g., pattern analysis, ABCD rule, 7-point checklist, 3-point checklist), clinical metadata (e.g., sex, age, anatomic site, familial history), and other details (e.g., timestamp, camera model) (Ca GLYPH&lt;11&gt; ery et al., 2018). We extensively discuss the image annotation issue in Section 4.1.


## 2.1. Datasets

The availability of larger, more diverse, and better-annotated datasets is one of the main driving factors for the advances in skin image analysis in the past decade (Marchetti et al., 2018; Celebi et al., 2019). Works in skin image analysis date back to the 1980s (Vanker and Van Stoecker, 1984; Dhawan et al., 1984), but until the mid-2000s, these works used small, private datasets, containing a few hundred images.

The Interactive Atlas of Dermoscopy (sometimes called the Edra Atlas , in reference to the publisher) by Argenziano et al. (2000) included a CD-ROM with 1 039 dermoscopy images (26% melanomas, 4% carcinomas, 70% nevi) of 1 024 ; ; GLYPH&lt;2&gt; 683 pixels, acquired by three European university hospitals (University of Graz, Austria, University of Naples, Italy, and University of Florence, Italy). The works of Celebi et al. (2007b, 2008) popularized the dataset in the dermoscopy image analysis community, where it became a de facto evaluation standard for almost a decade, until the much larger ISIC Archive datasets (see below) became available. Recently, Kawahara et al. (2019) placed this valuable dataset, along with additional textual annotations based on the 7-point checklist, in public domain under the name derm7pt . Shortly after the publication of the Interactive Atlas of Dermoscopy, Menzies et al. (2003) published An Atlas of Surface Microscopy of Pigmented Skin Lesions: Dermoscopy , with a CD-ROM containing 217 dermoscopic images (39% melanomas, 7% carcinomas, 54% nevi) of 712 GLYPH&lt;2&gt; 454 pixels, acquired at the Sydney Melanoma Unit, Australia.

The PH 2 dataset, released by Mendonca et al. (2013) and detailed by Mendonca et al. (2015), was the first public dataset to provide region-based annotations with segmentation masks, and masks for the clinically significant colors (white, red, light brown, dark brown, blue-gray, and black) present in the images. The dataset contains 200 dermoscopic images (20% melanomas, 40% atypical nevi, and 40% common nevi) of 768 GLYPH&lt;2&gt; 560 pixels, acquired at the Hospital Pedro Hispano, Portugal. The Edinburgh DermoFit Image Library (Ballerini et al., 2013) also provides region-based annotations for 1 ; 300 clinical images (10 diagnostic categories including melanomas, seborrhoeic keratosis, and basal cell carcinoma) of sizes ranging from 177 GLYPH&lt;2&gt; 189 to 2 ; 176 GLYPH&lt;2&gt; 2 549 ; pixels. The images were acquired with a Canon EOS 350D SLR camera, in controlled lighting and at a consistent distance from the lesions, resulting in a level of quality atypical for clinical images.

The ISIC Archive contains the world's largest curated repository of dermoscopic images. ISIC , an international academiaindustry partnership sponsored by ISDIS (International Society for Digital Imaging of the Skin), aims to 'facilitate the application of digital skin imaging to help reduce melanoma mortality' (ISIC, 2023). At the time of writing, the archive contains more than 240 000 images, of which more than 71 000 are publicly available. ; ; These images were acquired in leading worldwide clinical centers, using a variety of devices.

In addition to curating the datasets that collectively form the ' ISIC Archive', ISIC has released standard archive subsets as part of its Skin Lesion Analysis Towards Melanoma Detection Challenge, organized annually since 2016. The 2016, 2017, and 2018 challenges comprised segmentation, feature extraction, and classification tasks, while the 2019 and 2020 challenges featured

<!-- page_break -->

Table 1: Public skin lesion datasets with lesion segmentation annotations. All the datasets contain RGB images of skin lesions.

| dataset                                                                          |   year | modality   | size     | training / validation / test   | class distribution                                                                  | additional info                                                                                      |
|----------------------------------------------------------------------------------|--------|------------|----------|--------------------------------|-------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------|
| DermQuest 4 (DermQuest, 2012)                                                    |   2012 | clinical   | 137      | -                              | 61 non-melanomas 76 melanomas                                                       | acquired with di GLYPH<11> erent cameras under various lighting conditions                           |
| DermoFit (Ballerini et al., 2013)                                                |   2013 | clinical   | 1 ; 300  | -                              | 1 ; 224 non-melanomas 76 melanomas                                                  | sizes ranging from 177 GLYPH<2> 189 to 2 ; 176 GLYPH<2> 2 ; 549 pixels                               |
| Pedro Hispano Hospital (PH 2 ) (Mendonca et al., 2013)                           |   2013 | dermoscopy | 200      | -                              | 160 benign nevi 40 melanomas                                                        | sizes ranging from 553 GLYPH<2> 763 to 577 GLYPH<2> 769 pixels acquired at 20 GLYPH<2> magnification |
| ISIC2016 (Gutman et al., 2016)                                                   |   2016 | dermoscopy | 1 ; 279  | 900 / - / 379                  | Training: 727 non-melanomas 173 melanomas Test: 304 non-melanomas 75 melanomas      | sizes ranging from 566 GLYPH<2> 679 to 2 ; 848 GLYPH<2> 4 ; 288 pixels                               |
| ISIC2017 (Codella et al., 2018)                                                  |   2017 | dermoscopy | 2 ; 750  | 2 ; 000 / 150 / 600            | Training: 1 ; 626 non-melanomas 374 melanomas Test: 483 non-melanomas 117 melanomas | sizes ranging from 540 GLYPH<2> 722 to 4 ; 499 GLYPH<2> 6 ; 748 pixels                               |
| ISIC2018 (Codella et al., 2019)                                                  |   2018 | dermoscopy | 3 ; 694  | 2 ; 594 / 100 / 1 ; 000        | -                                                                                   | sizes ranging from 540 GLYPH<2> 576 to 4 ; 499 GLYPH<2> 6 ; 748 pixels                               |
| HAM10000 (Tschandl et al., 2018) (Tschandl et al., 2020) (ViDIR Dataverse, 2020) |   2020 | dermoscopy | 10 ; 015 | -                              | 1 ; 113 non-melanomas 8 ; 902 melanomas                                             | all images of 600 GLYPH<2> 450 pixels                                                                |

only classification. Each subset is associated with a challenge (year), one or more tasks, and has two (training test) or three / (training validation test) splits. / / The ISIC Challenge 2016 (Gutman et al., 2016) ( ISIC 2016, for brevity) contains 1 ; 279 images split into 900 for training (19% melanomas, 81% nevi), and 379 for testing (20% melanomas, 80% nevi). There is a large variation in image size, ranging from 0 5 to 12 megapixels. : All tasks used the same images. The ISIC 2017 (Codella et al., 2018) dataset more than doubled, with 2 750 images split into 2 ; ; 000 for training (18 7% melanomas, 12 7% seborrheic keratoses, 68 6% nevi), : : : 150 for validation (20% melanomas, 28% seborrheic keratoses, 52% nevi), and 600 for testing (19 5% melanomas, 15% seborrheic : keratoses, 65 5% nevi). Again, image size varied markedly, ranging from 0 5 to 29 megapixels, and all tasks used the same images. : :

ISIC 2018 provided, for the first time, separate datasets for the tasks, with 2 ; 594 training (20% melanomas, 72% nevi, and 8% seborrheic keratoses) and 100 1 / ; 000 for validation test images ranging from 0 5 to 29 megapixels, for the tasks of segmentation and / : feature extraction (Codella et al., 2019), and 10 ; 015 1 512 training test images for the classification task, all with 600 / ; / GLYPH&lt;2&gt; 450 pixels. The training dataset for classification was the HAM10000 dataset (Tschandl et al., 2018), acquired over a period of 20 years at the Medical University of Vienna, Austria and the private practice of Dr. Cli GLYPH&lt;11&gt; Rosendahl, Australia. It allowed a five-fold increase in training images in comparison to 2017 and comprised seven diagnostic categories: melanoma (11 1%), nevus (66 9%), basal : : cell carcinoma (5 1%), actinic keratosis or Bowen's disease (3 3%), benign keratosis (solar lentigo, seborrheic keratosis, or lichen : : planus-like keratosis, 11%), dermatofibroma (1 1%), and vascular lesion (1 4%). : : As a part of a 2020 study of human-computer collaboration for skin lesion diagnosis involving dermatologists and general practitioners (Tschandl et al., 2020), the lesions in the HAM10000 dataset were segmented by a single dermatologist and consequently released publicly (ViDIR Dataverse, 2020), making this the single largest publicly available skin lesion segmentation dataset (Table 1).

<!-- page_break -->

ISIC 2019 (Codella et al., 2018; Tschandl et al., 2018; Combalia et al., 2019) contains 25 ; 331 training images (18% melanomas, 51% nevi, 13% basal cell carcinomas, 3 5% actinic keratoses, 10% benign keratoses, 1% dermatofibromas, 1% vascular lesions, : and 2 5% squamous cell carcinomas) and 8 238 test images (diagnostic distribution unknown). The images range from 600 : ; GLYPH&lt;2&gt; 450 to 1 ; 024 GLYPH&lt;2&gt; 1 024 pixels. ;

ISIC 2020 (Rotemberg et al., 2021) contains 33 126 training images (1 8% melanomas, 97 6% nevi, 0 4% seborrheic ker-; : : : atoses, 0 1% lentigines simplex, 0 1% lichenoid keratoses, 0 02% solar lentigines, 0 003% cafe-au-lait macules, 0 003% atypical : : : : : melanocytic proliferations) and 10 ; 982 test images (diagnostic distribution unknown), ranging from 0.5 to 24 megapixels. Multiple centers, distributed worldwide, contributed to the dataset, including the Memorial Sloan Kettering Cancer Center ( USA ), the Melanoma Institute, the Sydney Melanoma Diagnostic Centre, and the University of Queensland (Australia), the Medical University of Vienna (Austria), the University of Athens (Greece), and the Hospital Clinic Barcelona (Spain). An important novelty in this dataset is the presence of multiple lesions per patient, with the express motivation of exploiting intra- and inter-patient lesion patterns, e.g., the so-called 'ugly-ducklings', lesions whose appearances are atypical for a given patient, and which present an increased risk of malignancy (Gachon et al., 2005).

There is, however, an overlap among these ISIC Challenge datasets. Abhishek (2020) analyzed all the lesion segmentation datasets from the ISIC Challenges (2016-2018) and found considerable overlap between these 3 datasets, with as many as 1 940 ; images shared between at least 2 datasets and 706 images shared between all 3 datasets. In a more recent analysis of the ISIC Challenge datasets for the lesion diagnosis task from 2016 through 2020, Cassidy et al. (2022) found overlap between the datasets as well as the presence of duplicates within the datasets. Using a duplicate removal strategy, they curated a new set of 45 ; 590 training images (8 61% melanomas, 91 39% others) and 11 397 validation images (8 61% melanomas, 91 39% others), leading : : ; : : to a total of 56 ; 987 images. Additionally, since the resulting dataset is highly imbalanced (melanomas versus others in a ratio of 1 : 10 62), the authors also curated a balanced dataset with 7 : ; 848 training images (50% melanoma, 50% others) and 1 962 ; validation images (50% melanoma, 50% others).

Table 1 shows a list of publicly available skin lesion datasets with pixel-wise annotations, image modality, sample size, original split sizes, and diagnostic distribution. Fig. 3 shows how frequently these datasets appear in the literature. It is also worth noting that several other skin lesion image datasets have not been described in our survey as they do not provide the corresponding skin lesion segmentation annotations. However, these datasets, including SD-198 (Sun et al., 2016), MED-NODE (Giotis et al., 2015), derm7pt (Kawahara et al., 2019), Interactive Dermatology Atlas (Usatine and Madden, 2013), Dermatology Information System (DermIS, 2012), DermWeb (Lui et al., 2009), DermNet New Zealand (Oakley et al., 1995), may still be relevant for skin lesion segmentation research (see Section 5).

Biases in computer vision datasets are a constant source of issues (Torralba and Efros, 2011), which is compounded in medical imaging due to the smaller number of samples, insu GLYPH&lt;14&gt; cient image resolution, lack of geographical or ethnic diversity, or statistics unrepresentative of clinical practice. All existing skin lesion datasets su GLYPH&lt;11&gt; er to a certain extent from one or more of the aforementioned issues, to which we add the specific issue of the availability and reliability of annotations. For lesion classification, many

4 DermQuest was deactivated on December 31, 2019. However, 137 of its images are publicly available (Glaister, 2013).

<!-- page_break -->

Fig. 3: The frequency of utilization of di GLYPH&lt;11&gt; erent skin lesion segmentation datasets in the surveyed studies. We found that 82 papers evaluated on more than 1 dataset, with 36 papers opting for cross-dataset evaluation ( CDE in Table 3). ISIC datasets ( ISIC 2016, ISIC 2017, ISIC 2018, and ISIC Archive) are used in the majority of papers, with 168 of 177 papers using at least one ISIC dataset and the ISIC 2017 dataset being the most popular (117 papers). The PH 2 dataset is the second most widely used (56 papers) following ISIC datasets.

picture_counter_12 The image is a pie chart depicting the distribution of different datasets used in a study. The datasets and their respective proportions are:

- ISIC 2017: 39.4%
- ISIC 2018: 21.2%
- PH²: 18.9%
- ISIC 2016: 13.8%
- DermoFit: 2.4%
- DermQuest: 2.0%

samples lack the gold standard histopathological confirmation, and ground-truth segmentation, even when available, is inherently noisy (Section 4.2). The presence of artifacts (Fig. 1) may lead to spurious correlations, an issue that Bissoto et al. (2019) attempted to quantify for classification models.


## 2.2. Synthetic Data Generation

Data augmentation-synthesizing new samples from existing ones-is commonly employed in the training of DL models. Augmented data serve as a regularizer, increase the amount and diversity of data (Shorten and Khoshgoftaar, 2019), induce desirable invariances on the model, and alleviate class imbalance. Traditional data augmentation applies simple geometric, photometric, and colorimetric transformations on the samples, including mirroring, translation, scaling, rotation, cropping, random region erasing, a GLYPH&lt;14&gt; ne or elastic deformation, modifications of hue, saturation, brightness, and contrast. Usually, several transformations are chosen at random and combined. Fig. 4 exemplifies the procedure, as applied to a dermoscopic image with Albumentations (Buslaev et al., 2020), a state-of-the-art open-source library for image augmentation.

As mentioned earlier, augmented training data induce invariance on the models: random translations and croppings, for example, help induce a translation-invariant model. This has implications for skin lesion analysis, e.g., data augmentation for generic datasets (such as ImageNet (Deng et al., 2009)) forgo vertical mirroring and large-angle rotations, because natural scenes have a strong vertical anisotropy, while skin lesion images are isotropic. In addition, augmented test data (test-time augmentation) may also improve generalization by combining the predictions of several augmented samples through, for example, average pooling or majority voting (Shorten and Khoshgoftaar, 2019). Perez et al. (2018) have systematically evaluated the e GLYPH&lt;11&gt; ect of several data

<!-- page_break -->

augmentation schemes for skin lesion classification, finding that the use of both training and test augmentation is critical for performance, surpassing, in some cases, increases of real data without augmentation. Valle et al. (2020) found, in a very large-scale experiment, that test-time augmentation was the second most influential factor for classification performance, after training set size. No systematic study of this kind exists for skin lesion segmentation.

picture_counter_13 The image shows a close-up view of a skin lesion under magnification, likely taken with a dermatoscope. The lesion appears irregular in shape with varying shades of brown and black, indicating pigmentation. Hair strands are visible across the lesion. This type of image is typically used in medical research papers or studies involving artificial intelligence techniques for diagnosing skin conditions such as melanoma.

picture_counter_14 The image shows a close-up view of a skin lesion with irregular borders and varied coloration, including dark brown and blue areas. The lesion is likely being analyzed for diagnostic purposes, potentially using artificial intelligence techniques to identify characteristics indicative of skin diseases such as melanoma.

picture_counter_15 The image shows a close-up view of a skin lesion, likely used in a medical research paper or a study involving artificial intelligence techniques for diagnosing diseases. The lesion appears irregular in shape with varied coloration, which may be indicative of a skin condition such as melanoma. The image is likely used to demonstrate the application of AI in analyzing skin lesions for medical diagnosis.

(a) Original

picture_counter_16 The image shows a close-up view of a skin lesion under magnification, possibly indicating melanoma. The lesion has irregular borders and varying shades of brown and black pigmentation. There are visible hair strands around and over the lesion.

(b) A GLYPH&lt;14&gt; ne deformation

(c) Elastic deformation

picture_counter_17 The image shows a close-up view of a skin lesion. The lesion appears irregular in shape with varying shades of red and dark areas, possibly indicating different pigmentation or tissue characteristics. There are several hairs visible around and over the lesion. This type of image is typically used in medical research for diagnosing skin conditions such as melanoma using artificial intelligence techniques.

(d) Histogram equalization

picture_counter_18 The image shows a close-up view of a skin lesion under magnification, likely taken with a dermatoscope. The lesion appears irregular in shape with varying colors, including shades of brown and blue. There are visible hair strands over the lesion. This image is relevant to medical research or a report focused on diagnosing skin conditions, potentially using artificial intelligence techniques.

(e) HSV shift

(f) RGB shift

Fig. 4: Various data augmentation transformations applied to a dermoscopic image (image source: ISIC 2016 dataset (Gutman et al., 2016)) using the Albumentations library (Buslaev et al., 2020).

Although traditional data augmentation is crucial for training DL models, it falls short of providing samples that are both diverse and plausible from the same distribution as real data. Thus, modern data augmentation (Tajbakhsh et al., 2020a) employs generative modeling, learning the probability distribution of the real data, and sampling from that distribution. Generative adversarial networks ( GAN s) (Goodfellow et al., 2020) are the most promising approach in this direction (Shorten and Khoshgoftaar, 2019), especially for medical image analysis (Yi et al., 2019; Kazeminia et al., 2020; Shamsolmoali et al., 2021). GAN s employ an adversarial training between a generator, which attempts to generate realistic fake samples, and a discriminator, which attempts to di GLYPH&lt;11&gt; erentiate real samples from fake ones. When the procedure converges, the generator output is surprisingly convincing, but GAN s are computationally expensive and di GLYPH&lt;14&gt; cult to train (Creswell et al., 2018).

Synthetic generation of skin lesions has received some recent interest, especially in the context of improving classification. Works can be roughly divided into those that use GAN s to create new images from a Gaussian latent variable (Baur et al., 2018; Pollastri et al., 2020; Abdelhalim et al., 2021), and those that implement GAN s based on image-to-image translation (Abhishek and Hamarneh, 2019; Bissoto et al., 2018; Ding et al., 2021).

Noise-based GAN s, such as DCGAN (Yu et al., 2017b), LAPGAN (Denton et al., 2015), and PGAN (Karras et al., 2018), learn to decode a Gaussian latent variable into an image that belongs to the training set distribution. The main advantage of these techniques

<!-- page_break -->

is the ability to create more, and more diverse images, as, in principle, any sample from a multivariate Gaussian distribution may become a di GLYPH&lt;11&gt; erent image. The disadvantage is that the images tend to be of lower quality, and, in the case of segmentation, one needs to generate plausible pairs of images and segmentation masks.

Image-to-image translation GAN s, such as pix2pix (Isola et al., 2017) and pix2pixHD (Wang et al., 2018), learn to create new samples from a semantic segmentation map. They have complementary advantages and disadvantages. Because the procedure is deterministic (one map creates one image), they have much less freedom in the number of samples available, but the images tend to be of higher quality (or more 'plausible'). There is no need to generate separate segmentation maps because the generated image is intrinsically compatible with the input segmentation map.

The two seminal papers on GAN s for skin lesions (Baur et al., 2018; Bissoto et al., 2018) evaluate several models. Baur et al. (2018) compare the noise-based DCGAN LAPGAN , , and PGAN for the generation of 256 GLYPH&lt;2&gt; 256-pixel images using both qualitative and quantitative criteria, finding that the PGAN gives considerably better results. They further examine the PGAN against a panel of human judges, composed by dermatologists and DL experts, in a 'visual Turing test', showing that both had di GLYPH&lt;14&gt; culties in distinguishing the fake images from the true ones. Bissoto et al. (2018) adapt the PGAN to be class-conditioned on diagnostic category, and the image-to-image pix2pixHD to employ the semantic annotation provided by the feature extraction task of the ISIC 2018 dataset (Section 1), comparing those to an unmodified DCGAN on 256 GLYPH&lt;2&gt; 256-pixel images, and finding the modified pix2pixHD to be qualitatively better. They use the performance improvement on a separate classification network as a quantitative metric, finding that the use of samples from both PGAN and pix2pixHD leads to the best improvements. They also showcase images of size up to 1 ; 024 GLYPH&lt;2&gt; 1 024 pixels generated by the pix2pixHD-derived model. ;

Pollastri et al. (2020) extended DCGAN and LAPGAN architectures to generate the segmentation masks (in the pairwise scheme explained above), making their work the only noise-based GAN s usable for segmentation to date. Bi et al. (2019a) introduced stacked adversarial learning to GAN s to learn class-specific skin lesion image generators given the ground-truth segmentations. Abhishek and Hamarneh (2019) employ pix2pix to translate a binary segmentation mask into a dermoscopic image and use the generated image-mask pairs to augment skin lesion segmentation training datasets, improving segmentation performance.

Ding et al. (2021) feed a segmentation mask and an instance mask to a conditional GAN generator, where the instance mask states the diagnostic category to be synthesized. In both cases, the discriminator receives di GLYPH&lt;11&gt; erent resolutions of the generated image and is required to make a decision for each of them. Abdelhalim et al. (2021) is a recent work that also conditions PGAN on the class label and uses the generated outputs to augment a melanoma diagnosis dataset.

Recently, Bissoto et al. (2021) cast doubt on the power of GAN -synthesized data augmentation to reliably improve skin lesion classification. Their evaluation, which included four GAN models, four datasets, and several augmentation scenarios, showed improvement only in a severe cross-modality scenario (training on dermoscopic and testing on clinical images). To the best of our knowledge, no corresponding systematic evaluation exists for skin lesion segmentation.


## 2.3. Supervised, Semi-supervised, Weakly supervised, Self-supervised learning

Although supervised DL has achieved outstanding performance in various medical image analysis applications, its dependency on high-quality annotations limits its applicability, as well as its generalizability to unseen, out-of-distribution data. Semi-supervised

<!-- page_break -->

Fig. 5: A breakdown of di GLYPH&lt;11&gt; erent levels of supervision used in the 177 surveyed works. Fully supervised models continue to make up the majority of the literature (163 papers), with semi-supervised and weakly supervised methods appearing in only 9 papers. Self-supervision in skin lesion segmentation is fairly new, with all the 5 papers appearing from 2020 onwards.

picture_counter_19 The image is a pie chart depicting the distribution of different types of learning methods used in diagnosing diseases. The chart shows that 92.1% of the methods are fully-supervised, 5.1% are semi- and weakly-supervised, and 2.8% are self-supervised.

techniques attempt to learn from both labeled and unlabeled samples. Weakly supervised techniques attempt to exploit partial annotations like image-level labels or bounding boxes, often in conjunction with a subset of pixel-level fully-annotated samples.

Since pixel-level annotation of skin lesion images is costly, there is a trade-o GLYPH&lt;11&gt; between annotation precision and e GLYPH&lt;14&gt; ciency. In practice, the annotations are intrinsically noisy, which can be modeled explicitly to avoid over-fitting. (We discuss the issue of annotation variability in Section 4.2.) To deal with label noise, Mirikharaji et al. (2019) learn a model robust to annotation noise, making use of a large set of unreliable annotations and a small set of perfect clean annotations. They propose to learn a spatially adaptive weight map corresponding to each training data, assigning di GLYPH&lt;11&gt; erent weights to noisy and clean pixel-level annotations while training the deep model. To remove the dependency on having a set of perfectly clean annotations, Redekop and Chernyavskiy (2021) propose to alter noisy ground-truth masks during training by considering the quantification of aleatoric uncertainty (Der Kiureghian and Ditlevsen, 2009; Gal, 2016; Depeweg et al., 2018; Kwon et al., 2020) to obtain a map of regions of high and low uncertainty. Pixels of ground-truth masks in highly uncertain regions are flipped, progressively increasing the model's robustness to label noise. Ribeiro et al. (2020) deal with noise by discarding inconsistent samples and annotation detail during training time, showing that the model generalizes better even when detailed annotations are required in test time.

When there is a labeled dataset, even if the number of labeled samples is far less than that of unlabeled samples, semi- and self-supervision techniques can be applied. Li et al. (2021c) propose a semi-supervised approach, using a transformation-consistent self-ensemble to leverage unlabeled data and to regularize the model. They minimize the di GLYPH&lt;11&gt; erence between the network predictions of di GLYPH&lt;11&gt; erent transformations (random perturbations, flipping, and rotation) applied to the input image and the transformation of the model prediction for the input image. Self-supervision attempts to exploit intrinsic labels by solving proxy tasks, enabling the use

<!-- page_break -->

of a large, unlabeled corpus of data to pretrain a model before fine-tuning it on the target task. An example is to artificially apply random rotations in the input images, and train the model to predict the exact degree of rotation (Gidaris et al., 2018). Note that the degree of rotation of each image is known, since it was artificially applied, and thus, can be used as a label during training. Similarly, for skin lesion segmentation, Li et al. (2020b) propose to exploit the color distribution information, the proxy task being to predict values from blue and red color channels while having the green one as input. They also include a task to estimate the red and blue color distributions to improve the model's ability to extract global features. After the pretraining, they use a smaller set of labeled data to fine-tune the model.


## 2.4. Image Preprocessing

Preprocessing may facilitate the segmentation of skin lesion images. Typical preprocessing operations include:

- GLYPH&lt;136&gt; Downsampling : Dermoscopy is typically a high-resolution technique, resulting in large image sizes, while many convolutional neural network ( CNN ) architectures, e.g., LeNet, AlexNet, VGG , GoogLeNet, ResNet, etc., require fixed-size input images, usually 224 GLYPH&lt;2&gt; 224 or 299 GLYPH&lt;2&gt; 299 pixels, and even those CNN s that can handle arbitrary-sized images (e.g., fullyconvolutional networks ( FCN s)) may benefit from downsampling for computational reasons. Downsampling is common in the skin lesion segmentation literature (Codella et al., 2017; Yu et al., 2017a; Yuan et al., 2017; Al-Masni et al., 2018; Zhang et al., 2019b; Pollastri et al., 2020).
- GLYPH&lt;136&gt; Color space transformations : RGB images are expected by most models, but some works (Codella et al., 2017; Al-Masni et al., 2018; Yuan and Lo, 2019; Pollastri et al., 2020; Pour and Seker, 2020) employ alternative color spaces (Busin et al., 2008), such as CIELAB CIELUV , , and HSV . Often, one or more channels of the transformed space are combined with the RGB channels for reasons including, but not limited to, increasing the class separability, decoupling luminance and chromaticity, ensuring (approximate) perceptual uniformity, achieving invariance to illumination or viewpoint, and eliminating highlights.
- GLYPH&lt;136&gt; Additional inputs : In addition to color space transformations, recent works incorporate more focused and domain-specific inputs to the segmentation models, such as Fourier domain representation using the discrete Fourier transform (Tang et al., 2021b) and inputs based on the physics of skin illumination and imaging (Abhishek et al., 2020).
- GLYPH&lt;136&gt; Contrast enhancement : Insu GLYPH&lt;14&gt; cient contrast (Fig. 1(i)) is a prime reason for segmentation failures (Bogo et al., 2015), leading some works (Saba et al., 2019; Schaefer et al., 2011) to enhance the image contrast prior to segmentation.
- GLYPH&lt;136&gt; Color normalization : Varying illumination (Barata et al., 2015a,b) may lead to inconsistencies in skin lesion segmentation. This problem can be addressed by color normalization (Goyal et al., 2019b).
- GLYPH&lt;136&gt; Artifact removal : Dermoscopic images often present artifacts, among which hair (Fig. 1(g)) is the most distracting (Abbas et al., 2011), leading some studies ( ¨ nver and Ayan, 2019; Zafar et al., 2020; Li et al., 2021b) to remove it prior to U segmentation.

Classical machine learning models (e.g., nearest neighbors, decision trees, support vector machines (Celebi et al., 2007b, 2008; Iyatomi et al., 2008; Barata et al., 2014; Shimizu et al., 2015)), which rely on hand-crafted features (Barata et al., 2019), tend

<!-- page_break -->

to benefit more from preprocessing than DL models, which, when properly trained, can learn from the data how to bypass input issues (Celebi et al., 2015a; Valle et al., 2020). However, preprocessing may still be helpful when dealing with small or noisy datasets.