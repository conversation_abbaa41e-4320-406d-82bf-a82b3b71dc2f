## Classification using pre-trained convolution neural network model

In this research, different versions of either full or segmented chest X-ray images have been introduced to CNN models to train the classifiers. Different experiments have been carried out on the original and segmented lung X-ray images both with their different enhanced versions. The classification has been done using   VGG19 14 and EfficientNetB0 16 pre-trained CNN models. After the calculation of different performance metrics, the best model has been selected as the adopted model. The next subsections give a brief description of the used pre-trained models.


## VGG19 model

VGG19 is a variant of the VGG CNN model which was created by Visual Geometry Group (VGG) at Oxford University. VGG19 was one of the winners of the Image Net Large Scale Visual Recognition Challenge (ILSVRC) in 2014. The size of the input image to VGG19 is (224 × 224). VGG19 contains 16 convolution layers, 5 maxpooling layers and 3 fully connected layers. The convolution layers are with (3 × 3) filters' size, stride of 1 pixel and padding of 1 pixel. The max-pooling layers are with a size of 2 × 2 and a stride of 2. The rectification (ReLU)

Vol.:(0123456789)

<!-- page_break -->

Vol:.(1234567890)

activation function is utilized for all hidden layers. Then, the first 2 fully connected layers with 4096 channels each are uitilized followed by the last layer of 1000 channels to represent the different 1000 classes of the ImageNet with soft-max activation   function 15 .


## EfficientNetB0 model

Google research group designed a family of models, called EfficientNets using a scaling method and achieved better efficiency and accuracy than previous ConvNets. EfficientNet is based on scaling CNNs and reaching better performance by balancing network width, depth, and resolution. Therefore, the focus is to present a scaling method to uniformly scale the 3 dimensions with a simple highly effective compound coefficient. Thus, it can be considered as an optimization problem to find the best coefficients for depth, width, and resolution that maximizes the accuracy of the network given the constraints of the available resources. The primary building block of the EfficientNet models is MBConv. The network's dimension equation was used to get the family of neural networks EfficientNet-B0 to   B7 16 . In this research, EfficientNetB0 was used for the classification of the chest X-ray images. Figure 4 sums up the framework of the adopted methodology in this research.


## Ethical approval

This article does not contain any studies with human participants or animals performed by the author.